<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Post Your Property - HavenHuts | List Your Room for Rent</title>
    <meta name="description" content="List your property on HavenHuts and connect with quality tenants. Easy property posting with photo uploads, detailed descriptions, and instant tenant contact.">
    <meta name="keywords" content="post property, list room, rent out room, property listing, HavenHuts">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://havenhuts.com/post-property">

    <!-- Fallback CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">

    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome with fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          onerror="this.onerror=null;this.href='https://use.fontawesome.com/releases/v6.4.0/css/all.css';">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <style>
        .drop-zone {
            border: 2px dashed #cbd5e0;
            transition: all 0.3s ease;
        }
        .drop-zone.dragover {
            border-color: #4F46E5;
            background-color: #eff6ff;
        }
        .image-preview {
            position: relative;
            display: inline-block;
        }
        .remove-image {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .mobile-menu {
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        .mobile-menu.open {
            transform: translateX(0);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-primary">
                        <i class="fas fa-home mr-2"></i>HavenHuts
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/" class="text-gray-700 hover:text-primary transition-colors font-medium">Home</a>
                    <a href="#" class="text-primary font-semibold">Post Property</a>
                    <a href="#" class="text-gray-700 hover:text-primary transition-colors font-medium">About</a>
                    <a href="#" class="text-gray-700 hover:text-primary transition-colors font-medium">Contact</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button id="mobileMenuBtn" class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="fas fa-bars text-gray-700 text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobileMenu" class="mobile-menu fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-50 md:hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-8">
                    <h2 class="text-xl font-bold text-primary">Menu</h2>
                    <button id="closeMobileMenu" class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-times text-gray-700 text-xl"></i>
                    </button>
                </div>
                <nav class="space-y-4">
                    <a href="/" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-home mr-3"></i>Home
                    </a>
                    <a href="#" class="block py-3 px-4 bg-primary text-white rounded-lg font-medium">
                        <i class="fas fa-plus mr-3"></i>Post Property
                    </a>
                    <a href="#" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-info-circle mr-3"></i>About
                    </a>
                    <a href="#" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-envelope mr-3"></i>Contact
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="bg-white rounded-2xl shadow-xl p-8 md:p-12">
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Post Your Property</h1>
                <p class="text-xl text-gray-600">Share your space with quality tenants and start earning today</p>
            </div>

            <!-- Property Code Input Section -->
            <div class="mb-8 p-6 bg-blue-50 rounded-xl border border-blue-200">
                <label for="propertyCode" class="block text-lg font-bold text-gray-900 mb-3">
                    Have a Property Code? Paste it below to edit your listing
                </label>
                <div class="flex flex-col space-y-3">
                    <input type="text" id="propertyCode" name="propertyCode"
                           placeholder="Paste your property code here (optional)"
                           class="w-full px-4 py-3 border border-blue-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                    <p class="text-sm text-gray-600">
                        <i class="fas fa-info-circle mr-1"></i>
                        (Optional) Paste your property code to update or delete an existing listing
                    </p>
                    <!-- Status messages -->
                    <div id="codeStatus" class="hidden">
                        <div id="codeSuccess" class="hidden text-green-700 bg-green-100 px-4 py-2 rounded-lg">
                            <i class="fas fa-check-circle mr-2"></i>
                            <span id="successMessage">Property loaded successfully! You can now edit or delete your listing.</span>
                        </div>
                        <div id="codeError" class="hidden text-red-700 bg-red-100 px-4 py-2 rounded-lg">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            <span id="errorMessage">Invalid or expired property code.</span>
                        </div>
                        <div id="codeLoading" class="hidden text-blue-700 bg-blue-100 px-4 py-2 rounded-lg">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            <span>Loading property details...</span>
                        </div>
                    </div>
                </div>
            </div>

            <form id="propertyForm" class="space-y-10">
                <!-- Photo Upload Section -->
                <div>
                    <label class="block text-xl font-bold text-gray-900 mb-6">Property Photos *</label>
                    <div id="dropZone" class="drop-zone p-12 text-center rounded-2xl cursor-pointer border-2 border-dashed border-gray-300 hover:border-primary transition-colors">
                        <div class="space-y-6">
                            <i class="fas fa-cloud-upload-alt text-6xl text-gray-400"></i>
                            <div>
                                <p class="text-xl text-gray-600 mb-2">Drag and drop photos here, or</p>
                                <button type="button" onclick="document.getElementById('fileInput').click()"
                                        class="text-primary hover:text-purple-700 font-semibold text-lg underline">
                                    click to browse
                                </button>
                            </div>
                            <p class="text-sm text-gray-500">Support: JPG, PNG, GIF (Max 5MB each, up to 10 photos)</p>
                        </div>
                    </div>
                    <input type="file" id="fileInput" multiple accept="image/*" class="hidden">

                    <!-- Image Previews -->
                    <div id="imagePreviewContainer" class="mt-8 grid grid-cols-2 md:grid-cols-4 gap-6"></div>
                </div>

                <!-- Property Details -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <label for="title" class="block text-lg font-bold text-gray-900 mb-3">Property Title *</label>
                        <input type="text" id="title" name="title" required
                               placeholder="e.g., Spacious 2BHK Flat in Salt Lake, Kolkata"
                               class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                    </div>

                    <div>
                        <label for="price" class="block text-lg font-bold text-gray-900 mb-3">Monthly Rent (₹) *</label>
                        <input type="number" id="price" name="price" required min="0" step="1000"
                               placeholder="25000"
                               class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <label for="address" class="block text-lg font-bold text-gray-900 mb-3">Address</label>
                        <input type="text" id="address" name="address"
                               placeholder="e.g., 123 Park Street, Near Victoria Memorial"
                               class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                        <p class="mt-2 text-sm text-gray-500">Optional - Specific address or landmark</p>
                    </div>

                    <div>
                        <label for="city" class="block text-lg font-bold text-gray-900 mb-3">City *</label>
                        <input type="text" id="city" name="city" required
                               placeholder="Kolkata"
                               class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <label for="bedrooms" class="block text-lg font-bold text-gray-900 mb-3">Bedrooms</label>
                        <select id="bedrooms" name="bedrooms"
                                class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                            <option value="">Select number of bedrooms (optional)</option>
                            <option value="1">1 Bedroom (Studio)</option>
                            <option value="2">2 Bedrooms</option>
                            <option value="3">3 Bedrooms</option>
                            <option value="4">4+ Bedrooms</option>
                        </select>
                    </div>

                    <div>
                        <label for="bathrooms" class="block text-lg font-bold text-gray-900 mb-3">Bathrooms</label>
                        <select id="bathrooms" name="bathrooms"
                                class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                            <option value="">Select number of bathrooms (optional)</option>
                            <option value="1">1 Bathroom</option>
                            <option value="2">2 Bathrooms</option>
                            <option value="3">3+ Bathrooms</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <label for="furnished_status" class="block text-lg font-bold text-gray-900 mb-3">Furnished Status</label>
                        <select id="furnished_status" name="furnished_status"
                                class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg">
                            <option value="">Select furnished status (optional)</option>
                            <option value="furnished">Fully Furnished</option>
                            <option value="semi">Semi Furnished</option>
                            <option value="none">Unfurnished</option>
                        </select>
                    </div>

                    <div>
                        <label for="phone" class="block text-lg font-bold text-gray-900 mb-3">WhatsApp Number *</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <span class="text-gray-500 text-lg font-medium">+91</span>
                            </div>
                            <input type="tel" id="phone" name="phone" required
                                   placeholder="98765 43210"
                                   class="w-full pl-16 pr-4 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-lg"
                                   maxlength="10"
                                   pattern="[0-9]{10}">
                        </div>
                        <p class="mt-2 text-sm text-gray-600">
                            <i class="fab fa-whatsapp text-green-500 mr-1"></i>
                            Just enter your 10-digit mobile number (we'll add +91 automatically)
                        </p>
                    </div>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-lg font-bold text-gray-900 mb-3">Property Description</label>
                    <textarea id="description" name="description" rows="6"
                              placeholder="Describe your property... Include details about the neighborhood, nearby amenities, transportation, what makes your property special, and any house rules. (Optional but recommended for better responses)"
                              class="w-full px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent resize-none text-lg"></textarea>
                    <p class="mt-3 text-sm text-gray-500">Optional - Detailed descriptions get more inquiries!</p>
                </div>

                <!-- Amenities -->
                <div>
                    <label class="block text-lg font-bold text-gray-900 mb-6">Amenities & Features</label>
                    <p class="text-sm text-gray-500 mb-4">Select all amenities that apply to your property (optional)</p>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="WiFi" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">WiFi</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Air Conditioning" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Air Conditioning</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Parking" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Parking</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Laundry" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Laundry</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Pet Friendly" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Pet Friendly</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Gym Access" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Gym Access</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Balcony" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Balcony</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="24/7 Security" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">24/7 Security</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer p-3 rounded-lg hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="amenities" value="Swimming Pool" class="rounded border-gray-300 text-primary focus:ring-primary w-5 h-5">
                            <span class="text-gray-700 font-medium">Swimming Pool</span>
                        </label>
                    </div>
                </div>



                <!-- Submit and Delete Buttons -->
                <div class="text-center pt-8 space-y-4">
                    <button type="submit" id="submitBtn"
                            class="bg-primary text-white px-12 py-5 rounded-xl text-xl font-bold hover:bg-purple-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed shadow-lg">
                        <span id="submitText">Post Property</span>
                        <i id="submitSpinner" class="fas fa-spinner fa-spin ml-3 hidden"></i>
                    </button>

                    <!-- Delete Button (hidden by default, shown in edit mode) -->
                    <div id="deleteSection" class="hidden">
                        <button type="button" id="deleteBtn"
                                class="bg-red-600 text-white px-8 py-3 rounded-xl text-lg font-bold hover:bg-red-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed shadow-lg">
                            <i class="fas fa-trash mr-2"></i>
                            <span id="deleteText">Delete this listing</span>
                            <i id="deleteSpinner" class="fas fa-spinner fa-spin ml-3 hidden"></i>
                        </button>
                        <p class="mt-2 text-sm text-red-600">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            This action cannot be undone
                        </p>
                    </div>

                    <p id="submitNote" class="mt-4 text-gray-500">Your listing will be reviewed and published within 24 hours</p>
                </div>
            </form>
        </div>
    </main>

    <!-- Success Modal -->
    <div id="successModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-2xl p-8 max-w-md mx-4 text-center">
            <div class="mb-6">
                <i class="fas fa-check-circle text-6xl text-secondary mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">Property Posted Successfully!</h3>
                <p class="text-gray-600">Your property listing has been submitted and will be reviewed within 24 hours.</p>
            </div>
            <div class="space-y-3">
                <button onclick="closeSuccessModal()"
                        class="w-full bg-primary text-white py-3 px-6 rounded-xl hover:bg-purple-700 transition-colors font-semibold">
                    Post Another Property
                </button>
                <a href="/"
                   class="block w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-xl hover:bg-gray-300 transition-colors text-center font-semibold">
                    View All Properties
                </a>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let editMode = false;
        let currentPropertyId = null;
        let currentEditHash = null;

        // Initialize functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeMobileMenu();
            initializeFileUpload();
            initializeForm();
            initializePropertyCodeInput();
        });

        // Mobile menu functionality
        function initializeMobileMenu() {
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');
            const closeMobileMenu = document.getElementById('closeMobileMenu');

            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.add('open');
                document.body.style.overflow = 'hidden';
            });

            closeMobileMenu.addEventListener('click', () => {
                mobileMenu.classList.remove('open');
                document.body.style.overflow = 'auto';
            });

            mobileMenu.addEventListener('click', (e) => {
                if (e.target === mobileMenu) {
                    mobileMenu.classList.remove('open');
                    document.body.style.overflow = 'auto';
                }
            });
        }

        // Initialize file upload functionality
        function initializeFileUpload() {
            const dropZone = document.getElementById('dropZone');
            const fileInput = document.getElementById('fileInput');

            dropZone.addEventListener('dragover', handleDragOver);
            dropZone.addEventListener('dragleave', handleDragLeave);
            dropZone.addEventListener('drop', handleDrop);
            fileInput.addEventListener('change', handleFileSelect);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');

            const files = Array.from(e.dataTransfer.files);
            addFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            addFiles(files);
        }

        function addFiles(files) {
            const validFiles = files.filter(file => {
                if (!file.type.startsWith('image/')) {
                    alert(`${file.name} is not an image file.`);
                    return false;
                }
                if (file.size > 5 * 1024 * 1024) {
                    alert(`${file.name} is too large. Maximum size is 5MB.`);
                    return false;
                }
                return true;
            });

            if (selectedFiles.length + validFiles.length > 10) {
                alert('Maximum 10 photos allowed.');
                return;
            }

            selectedFiles = [...selectedFiles, ...validFiles];
            updateImagePreviews();
        }

        function updateImagePreviews() {
            const container = document.getElementById('imagePreviewContainer');
            container.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.createElement('div');
                    preview.className = 'image-preview';
                    preview.innerHTML = `
                        <img src="${e.target.result}" alt="Preview ${index + 1}" class="w-full h-32 object-cover rounded-xl shadow-md">
                        <button type="button" onclick="removeImage(${index})" class="remove-image hover:bg-red-600 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    container.appendChild(preview);
                };
                reader.readAsDataURL(file);
            });
        }

        function removeImage(index) {
            selectedFiles.splice(index, 1);
            updateImagePreviews();
        }

        // Initialize property code input functionality
        function initializePropertyCodeInput() {
            const propertyCodeInput = document.getElementById('propertyCode');
            const deleteBtn = document.getElementById('deleteBtn');

            // Add event listener for property code input
            propertyCodeInput.addEventListener('input', debounce(handlePropertyCodeInput, 500));

            // Add event listener for delete button
            deleteBtn.addEventListener('click', handleDeleteProperty);
        }

        // Debounce function to limit API calls
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Handle property code input
        async function handlePropertyCodeInput() {
            const propertyCode = document.getElementById('propertyCode').value.trim();

            if (!propertyCode) {
                resetToCreateMode();
                return;
            }

            showCodeStatus('loading');

            try {
                const response = await fetch('/api/manage-property', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ edit_hash: propertyCode })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        loadPropertyForEdit(result.property, propertyCode);
                        showCodeStatus('success');
                    } else {
                        showCodeStatus('error', result.error);
                    }
                } else {
                    showCodeStatus('error', 'Invalid or expired property code.');
                }
            } catch (error) {
                console.error('Error loading property:', error);
                showCodeStatus('error', 'Failed to load property. Please try again.');
            }
        }

        // Show code status messages
        function showCodeStatus(type, message = '') {
            const codeStatus = document.getElementById('codeStatus');
            const codeSuccess = document.getElementById('codeSuccess');
            const codeError = document.getElementById('codeError');
            const codeLoading = document.getElementById('codeLoading');

            // Hide all status messages
            codeStatus.classList.add('hidden');
            codeSuccess.classList.add('hidden');
            codeError.classList.add('hidden');
            codeLoading.classList.add('hidden');

            if (type === 'success') {
                codeStatus.classList.remove('hidden');
                codeSuccess.classList.remove('hidden');
            } else if (type === 'error') {
                codeStatus.classList.remove('hidden');
                codeError.classList.remove('hidden');
                if (message) {
                    document.getElementById('errorMessage').textContent = message;
                }
            } else if (type === 'loading') {
                codeStatus.classList.remove('hidden');
                codeLoading.classList.remove('hidden');
            }
        }

        // Load property data for editing
        function loadPropertyForEdit(property, editHash) {
            editMode = true;
            currentPropertyId = property._id;
            currentEditHash = editHash;

            // Update form fields
            document.getElementById('title').value = property.title || '';
            document.getElementById('price').value = property.price || '';
            document.getElementById('city').value = property.city || '';

            // Handle phone number - remove +91 prefix for display
            let phoneValue = property.phone || '';
            if (phoneValue.startsWith('+91 ')) {
                phoneValue = phoneValue.substring(4); // Remove '+91 '
            } else if (phoneValue.startsWith('+91')) {
                phoneValue = phoneValue.substring(3); // Remove '+91'
            }
            document.getElementById('phone').value = phoneValue;

            document.getElementById('address').value = property.address || '';
            document.getElementById('description').value = property.description || '';

            // Set select fields
            if (property.bedrooms) {
                document.getElementById('bedrooms').value = property.bedrooms;
            }
            if (property.bathrooms) {
                document.getElementById('bathrooms').value = property.bathrooms;
            }
            if (property.furnished_status) {
                document.getElementById('furnished_status').value = property.furnished_status;
            }

            // Set amenities checkboxes
            if (property.amenities && Array.isArray(property.amenities)) {
                const amenityCheckboxes = document.querySelectorAll('input[name="amenities"]');
                amenityCheckboxes.forEach(checkbox => {
                    checkbox.checked = property.amenities.includes(checkbox.value);
                });
            }

            // Update UI for edit mode
            document.getElementById('submitText').textContent = 'Update Property';
            document.getElementById('deleteSection').classList.remove('hidden');
            document.getElementById('submitNote').textContent = 'Your changes will be saved immediately.';

            // Make image upload optional in edit mode
            const photoLabel = document.querySelector('label[for="fileInput"]').parentElement.querySelector('label');
            if (photoLabel) {
                photoLabel.innerHTML = 'Property Photos <span class="text-sm text-gray-500">(Optional - leave empty to keep existing photos)</span>';
            }

            // Display existing images if available
            if (property.image_urls && property.image_urls.length > 0) {
                displayExistingImages(property.image_urls);
            }
        }

        // Display existing images
        function displayExistingImages(imageUrls) {
            const container = document.getElementById('imagePreviewContainer');
            container.innerHTML = '';

            imageUrls.forEach((url, index) => {
                const preview = document.createElement('div');
                preview.className = 'image-preview';
                preview.innerHTML = `
                    <img src="${url}" alt="Existing Image ${index + 1}" class="w-full h-32 object-cover rounded-xl shadow-md">
                    <div class="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded text-xs">
                        Existing
                    </div>
                `;
                container.appendChild(preview);
            });
        }

        // Reset to create mode
        function resetToCreateMode() {
            editMode = false;
            currentPropertyId = null;
            currentEditHash = null;

            // Reset form
            document.getElementById('propertyForm').reset();
            selectedFiles = [];
            updateImagePreviews();

            // Reset UI
            document.getElementById('submitText').textContent = 'Post Property';
            document.getElementById('deleteSection').classList.add('hidden');
            document.getElementById('submitNote').textContent = 'Your listing will be reviewed and published within 24 hours';

            // Reset photo label
            const photoLabel = document.querySelector('label[for="fileInput"]').parentElement.querySelector('label');
            if (photoLabel) {
                photoLabel.innerHTML = 'Property Photos *';
            }

            // Hide status messages
            showCodeStatus('');
        }

        // Handle delete property
        async function handleDeleteProperty() {
            if (!currentPropertyId || !currentEditHash) {
                alert('No property selected for deletion.');
                return;
            }

            if (!confirm('Are you sure you want to delete this property? This action cannot be undone.')) {
                return;
            }

            const deleteBtn = document.getElementById('deleteBtn');
            const deleteText = document.getElementById('deleteText');
            const deleteSpinner = document.getElementById('deleteSpinner');

            // Show loading state
            deleteBtn.disabled = true;
            deleteText.textContent = 'Deleting...';
            deleteSpinner.classList.remove('hidden');

            try {
                const response = await fetch(`/api/properties/${currentPropertyId}?hash=${currentEditHash}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        alert('Property deleted successfully!');
                        resetToCreateMode();
                        document.getElementById('propertyCode').value = '';
                    } else {
                        alert('Failed to delete property: ' + result.error);
                    }
                } else {
                    alert('Failed to delete property. Please try again.');
                }
            } catch (error) {
                console.error('Error deleting property:', error);
                alert('Failed to delete property. Please check your connection and try again.');
            } finally {
                // Reset button state
                deleteBtn.disabled = false;
                deleteText.textContent = 'Delete this listing';
                deleteSpinner.classList.add('hidden');
            }
        }

        // Initialize form functionality
        function initializeForm() {
            document.getElementById('propertyForm').addEventListener('submit', handleFormSubmit);

            // Phone number formatting - only allow digits and limit to 10 characters
            document.getElementById('phone').addEventListener('input', function(e) {
                // Remove all non-digit characters
                let value = e.target.value.replace(/\D/g, '');

                // Limit to 10 digits
                if (value.length > 10) {
                    value = value.substring(0, 10);
                }

                // Update the input value
                e.target.value = value;
            });

            // Add visual feedback for phone number validation
            document.getElementById('phone').addEventListener('blur', function(e) {
                const value = e.target.value.trim();
                if (value && value.length !== 10) {
                    e.target.style.borderColor = '#ef4444';
                    e.target.style.boxShadow = '0 0 0 1px #ef4444';
                } else {
                    e.target.style.borderColor = '';
                    e.target.style.boxShadow = '';
                }
            });
        }

        async function handleFormSubmit(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const submitSpinner = document.getElementById('submitSpinner');

            if (!validateForm()) {
                return;
            }

            // Show loading state
            submitBtn.disabled = true;
            const originalText = submitText.textContent;
            submitText.textContent = editMode ? 'Updating Property...' : 'Posting Property...';
            submitSpinner.classList.remove('hidden');

            try {
                const formData = new FormData();

                // Add required form fields
                formData.append('title', document.getElementById('title').value);
                formData.append('price', document.getElementById('price').value);
                formData.append('city', document.getElementById('city').value);

                // Format phone number with +91 prefix
                const phoneValue = document.getElementById('phone').value.trim();
                const formattedPhone = phoneValue.startsWith('+91') ? phoneValue : `+91 ${phoneValue}`;
                formData.append('phone', formattedPhone);

                // Add optional fields (with values if provided, empty if not)
                formData.append('address', document.getElementById('address').value || '');
                formData.append('bedrooms', document.getElementById('bedrooms').value || '');
                formData.append('bathrooms', document.getElementById('bathrooms').value || '');
                formData.append('furnished_status', document.getElementById('furnished_status').value || '');
                formData.append('description', document.getElementById('description').value || '');

                // Add amenities
                const amenities = Array.from(document.querySelectorAll('input[name="amenities"]:checked'))
                    .map(checkbox => checkbox.value);
                formData.append('amenities', JSON.stringify(amenities));

                // Add images (only if new files selected)
                selectedFiles.forEach((file, index) => {
                    formData.append(`image_${index}`, file);
                });

                // Add metadata for SEO
                formData.append('slug', generateSlug(document.getElementById('title').value));
                formData.append('timestamp', new Date().toISOString());

                let response;
                if (editMode) {
                    // Update existing property
                    response = await fetch(`/api/properties/${currentPropertyId}?hash=${currentEditHash}`, {
                        method: 'PUT',
                        body: formData
                    });
                } else {
                    // Create new property
                    response = await fetch('/api/properties', {
                        method: 'POST',
                        body: formData
                    });
                }

                if (!response.ok) {
                    throw new Error(editMode ? 'Failed to update property' : 'Failed to post property');
                }

                const result = await response.json();

                if (result.success) {
                    if (editMode) {
                        // Show success message for update
                        alert('Property updated successfully!');
                        // Optionally reload the property to show updated data
                        // handlePropertyCodeInput();
                    } else {
                        // Show success modal for new property and display edit hash
                        showSuccessWithEditHash(result.edit_hash);
                    }
                } else {
                    throw new Error(result.error || 'Unknown error occurred');
                }

            } catch (error) {
                console.error('Error submitting property:', error);
                alert(editMode ?
                    'Failed to update property. Please check your connection and try again.' :
                    'Failed to post property. Please check your connection and try again.'
                );
            } finally {
                // Reset button state
                submitBtn.disabled = false;
                submitText.textContent = originalText;
                submitSpinner.classList.add('hidden');
            }
        }

        // Show success modal with edit hash for new properties
        function showSuccessWithEditHash(editHash) {
            // Update success modal content to include edit hash
            const successModal = document.getElementById('successModal');
            const modalContent = successModal.querySelector('.bg-white');

            modalContent.innerHTML = `
                <div class="mb-6">
                    <i class="fas fa-check-circle text-6xl text-secondary mb-4"></i>
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Property Posted Successfully!</h3>
                    <p class="text-gray-600 mb-4">Your property listing has been submitted and will be reviewed within 24 hours.</p>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <h4 class="font-bold text-blue-900 mb-2">
                            <i class="fas fa-key mr-2"></i>Your Property Code
                        </h4>
                        <div class="bg-white border border-blue-300 rounded p-3 mb-3">
                            <input type="text" value="${editHash}" readonly
                                   class="w-full text-center font-mono text-sm text-blue-800 bg-transparent border-none outline-none cursor-pointer"
                                   onclick="this.select()"
                                   title="Click to select all">
                        </div>
                        <p class="text-sm text-blue-700">
                            <i class="fas fa-info-circle mr-1"></i>
                            Save this code! You can use it to edit or delete your listing anytime.
                        </p>
                        <p class="text-xs text-blue-600 mt-2">
                            💡 Tip: Click the code above to select it, then copy with Ctrl+C
                        </p>
                    </div>
                </div>
                <div class="space-y-3">
                    <button onclick="copyEditHash('${editHash}')"
                            class="w-full bg-blue-600 text-white py-3 px-6 rounded-xl hover:bg-blue-700 transition-colors font-semibold">
                        <i class="fas fa-copy mr-2"></i>Copy Property Code
                    </button>
                    <button onclick="closeSuccessModal()"
                            class="w-full bg-primary text-white py-3 px-6 rounded-xl hover:bg-purple-700 transition-colors font-semibold">
                        Post Another Property
                    </button>
                    <a href="/"
                       class="block w-full bg-gray-200 text-gray-800 py-3 px-6 rounded-xl hover:bg-gray-300 transition-colors text-center font-semibold">
                        View All Properties
                    </a>
                </div>
            `;

            successModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // Copy edit hash to clipboard with fallback
        function copyEditHash(editHash) {
            // Try modern clipboard API first
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(editHash).then(() => {
                    showCopySuccess();
                }).catch(() => {
                    fallbackCopy(editHash);
                });
            } else {
                // Fallback for older browsers or non-HTTPS
                fallbackCopy(editHash);
            }
        }

        // Fallback copy method
        function fallbackCopy(editHash) {
            try {
                // Create a temporary textarea
                const textArea = document.createElement('textarea');
                textArea.value = editHash;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();

                // Try to copy using execCommand
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);

                if (successful) {
                    showCopySuccess();
                } else {
                    showCopyFallback(editHash);
                }
            } catch (err) {
                showCopyFallback(editHash);
            }
        }

        // Show copy success feedback
        function showCopySuccess() {
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check mr-2"></i>Copied!';
            button.classList.add('bg-green-600');
            button.classList.remove('bg-blue-600');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-600');
                button.classList.add('bg-blue-600');
            }, 2000);
        }

        // Show manual copy option
        function showCopyFallback(editHash) {
            // Create a modal for manual copy
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl p-6 max-w-md mx-4 text-center">
                    <div class="mb-4">
                        <i class="fas fa-copy text-4xl text-blue-600 mb-3"></i>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">Copy Your Property Code</h3>
                        <p class="text-gray-600 mb-4">Please copy this code manually:</p>
                    </div>

                    <div class="bg-gray-100 border border-gray-300 rounded-lg p-4 mb-4">
                        <input type="text" value="${editHash}" readonly
                               class="w-full text-center font-mono text-sm bg-transparent border-none outline-none"
                               onclick="this.select()" id="manualCopyInput">
                    </div>

                    <div class="space-y-3">
                        <button onclick="selectAndCopy()"
                                class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-mouse-pointer mr-2"></i>Select All
                        </button>
                        <button onclick="closeManualCopyModal()"
                                class="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors">
                            Close
                        </button>
                    </div>

                    <p class="text-xs text-gray-500 mt-3">
                        Tip: Click the code above to select it, then press Ctrl+C (or Cmd+C on Mac)
                    </p>
                </div>
            `;

            document.body.appendChild(modal);

            // Auto-select the text
            setTimeout(() => {
                const input = document.getElementById('manualCopyInput');
                input.focus();
                input.select();
            }, 100);

            // Close on background click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeManualCopyModal();
                }
            });
        }

        // Select all text in manual copy input
        function selectAndCopy() {
            const input = document.getElementById('manualCopyInput');
            input.focus();
            input.select();

            // Try to copy again
            try {
                document.execCommand('copy');
                // Show success message
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check mr-2"></i>Copied!';
                button.classList.add('bg-green-600');
                button.classList.remove('bg-blue-600');

                setTimeout(() => {
                    closeManualCopyModal();
                }, 1000);
            } catch (err) {
                // Just keep the text selected for manual copy
            }
        }

        // Close manual copy modal
        function closeManualCopyModal() {
            const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
            if (modal) {
                document.body.removeChild(modal);
            }
        }

        function validateForm() {
            const title = document.getElementById('title').value.trim();
            const price = document.getElementById('price').value;
            const city = document.getElementById('city').value.trim();
            const phone = document.getElementById('phone').value.trim();

            // Only validate required fields
            if (!title || !price || !city || !phone) {
                alert('Please fill in all required fields marked with * :\n• Property Title\n• Monthly Rent (₹)\n• City\n• WhatsApp Number\n\nAll other fields are optional but recommended for better responses.');
                return false;
            }

            // Validate title length
            if (title.length < 10) {
                alert('Property title must be at least 10 characters long.');
                return false;
            }

            // Validate price
            if (isNaN(price) || parseFloat(price) <= 0) {
                alert('Please enter a valid monthly rent amount.');
                return false;
            }

            // Validate phone number format (10 digits only)
            const phoneDigits = phone.replace(/\D/g, '');
            if (phoneDigits.length !== 10) {
                alert('Please enter a valid 10-digit mobile number (e.g., 9876543210).');
                return false;
            }

            // Optional: Validate description length if provided
            const description = document.getElementById('description').value.trim();
            if (description && description.length < 20) {
                alert('If you provide a description, it should be at least 20 characters long for better results.');
                return false;
            }

            // Image validation - required for new properties, optional for updates
            if (!editMode && selectedFiles.length === 0) {
                alert('Please upload at least one photo of your property.');
                return false;
            }

            return true;
        }

        function generateSlug(title) {
            return title.toLowerCase()
                .replace(/[^a-z0-9 -]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
        }

        function closeSuccessModal() {
            document.getElementById('successModal').classList.add('hidden');
            document.body.style.overflow = 'auto';

            // Reset form for new property
            resetToCreateMode();
            document.getElementById('propertyCode').value = '';
        }
    </script>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Post Your Property - HavenHuts",
        "description": "List your property on HavenHuts and connect with quality tenants",
        "url": "https://havenhuts.com/post-property",
        "mainEntity": {
            "@type": "Service",
            "name": "Property Listing Service",
            "provider": {
                "@type": "Organization",
                "name": "HavenHuts"
            }
        }
    }
    </script>
</body>
</html>