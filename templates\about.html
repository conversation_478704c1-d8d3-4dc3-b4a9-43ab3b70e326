<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - HavenHuts | Our Story & Mission</title>
    <meta name="description" content="Learn about HavenHuts - Founded by college students who faced room finding challenges in Haldia. Our mission is to make rental room searching easy and broker-free.">
    <meta name="keywords" content="about havenhuts, college students startup, room finding problems, haldia, rental platform, broker-free">
    <meta name="author" content="HavenHuts">
    <meta property="og:title" content="About HavenHuts - College Students Solving Room Finding Problems">
    <meta property="og:description" content="Founded by college students who experienced room finding difficulties in Haldia. Making rental searches easy and broker-free.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://havenhuts.com/about">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://havenhuts.com/about">

    <!-- Fallback CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-text {
            background: linear-gradient(135deg, #4F46E5, #10B981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .story-card {
            transition: all 0.3s ease;
        }
        .story-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <a href="/" class="text-2xl font-bold text-primary">
                        <i class="fas fa-home mr-2"></i>HavenHuts
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/" class="text-gray-700 hover:text-primary transition-colors font-medium">Home</a>
                    <a href="/post-property" class="text-gray-700 hover:text-primary transition-colors font-medium">Post Property</a>
                    <a href="/about" class="text-primary font-semibold border-b-2 border-primary">About</a>
                    <a href="#" class="text-gray-700 hover:text-primary transition-colors font-medium">Contact</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button id="mobileMenuBtn" class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="fas fa-bars text-gray-700 text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobileMenu" class="mobile-menu fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-50 md:hidden transform translate-x-full transition-transform duration-300">
            <div class="p-6">
                <div class="flex justify-between items-center mb-8">
                    <h2 class="text-xl font-bold text-primary">Menu</h2>
                    <button id="closeMobileMenu" class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-times text-gray-700 text-xl"></i>
                    </button>
                </div>
                <nav class="space-y-4">
                    <a href="/" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-home mr-3"></i>Home
                    </a>
                    <a href="/post-property" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-plus mr-3"></i>Post Property
                    </a>
                    <a href="/about" class="block py-3 px-4 bg-primary text-white rounded-lg font-semibold">
                        <i class="fas fa-info-circle mr-3"></i>About
                    </a>
                    <a href="#" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-envelope mr-3"></i>Contact
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-primary to-purple-600 text-white py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Our Story</h1>
            <p class="text-xl md:text-2xl mb-8 text-purple-100">From college struggles to startup solutions</p>
            <div class="flex justify-center items-center space-x-4 text-lg">
                <span class="bg-white bg-opacity-20 px-4 py-2 rounded-full">🎓 College Students</span>
                <span class="bg-white bg-opacity-20 px-4 py-2 rounded-full">🚀 Startup Founders</span>
                <span class="bg-white bg-opacity-20 px-4 py-2 rounded-full">💡 Problem Solvers</span>
            </div>
        </div>
    </section>

    <!-- Our Story Section -->
    <section class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">The Problem That Started It All</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">Every great solution begins with a real problem. Ours started in the hostels and streets of Haldia.</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
                <div>
                    <div class="bg-red-50 border-l-4 border-red-400 p-6 mb-8">
                        <h3 class="text-2xl font-bold text-red-800 mb-4">
                            <i class="fas fa-exclamation-triangle mr-2"></i>The Struggle Was Real
                        </h3>
                        <ul class="space-y-3 text-red-700">
                            <li class="flex items-start">
                                <i class="fas fa-times-circle mr-3 mt-1 text-red-500"></i>
                                <span>Endless searching through unreliable brokers</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-times-circle mr-3 mt-1 text-red-500"></i>
                                <span>Hidden fees and commission charges</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-times-circle mr-3 mt-1 text-red-500"></i>
                                <span>Fake listings and misleading information</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-times-circle mr-3 mt-1 text-red-500"></i>
                                <span>No direct contact with property owners</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-times-circle mr-3 mt-1 text-red-500"></i>
                                <span>Wasted time and money on false promises</span>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="bg-white rounded-2xl shadow-xl p-8">
                    <div class="text-center">
                        <div class="w-20 h-20 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-lightbulb text-3xl text-primary"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">The "Aha!" Moment</h3>
                        <p class="text-gray-600 text-lg leading-relaxed">
                            "Why should students like us suffer through this broken system? What if we could connect directly with property owners, skip the middlemen, and make room finding actually easy?"
                        </p>
                        <div class="mt-6 text-primary font-semibold">
                            - The HavenHuts Founding Team
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Team Section -->
    <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Meet the Team</h2>
                <p class="text-xl text-gray-600">College friends turned co-founders, united by a common mission</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <div class="story-card bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-8 text-center">
                    <div class="w-24 h-24 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-code text-3xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">The Developers</h3>
                    <p class="text-gray-600">Turning late-night coding sessions into a platform that actually works</p>
                </div>

                <div class="story-card bg-gradient-to-br from-green-50 to-emerald-100 rounded-2xl p-8 text-center">
                    <div class="w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-users text-3xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">The Visionaries</h3>
                    <p class="text-gray-600">Dreaming of a broker-free world where students find homes easily</p>
                </div>

                <div class="story-card bg-gradient-to-br from-purple-50 to-violet-100 rounded-2xl p-8 text-center">
                    <div class="w-24 h-24 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-heart text-3xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-2">The Believers</h3>
                    <p class="text-gray-600">Passionate about making a real difference in students' lives</p>
                </div>
            </div>

            <div class="bg-gradient-to-r from-primary to-purple-600 rounded-2xl p-8 text-white text-center">
                <h3 class="text-2xl font-bold mb-4">We're Just Getting Started</h3>
                <p class="text-lg text-purple-100 max-w-3xl mx-auto">
                    As college students ourselves, we understand the challenges you face. We're building HavenHuts not just as a business, 
                    but as a solution to a problem we've lived through. Every feature, every improvement comes from real experience and genuine care.
                </p>
            </div>
        </div>
    </section>

    <!-- Mission & Vision -->
    <section class="py-20 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div class="bg-white rounded-2xl shadow-xl p-8">
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-bullseye text-2xl text-primary"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">Our Mission</h3>
                    </div>
                    <p class="text-gray-600 text-lg leading-relaxed text-center">
                        To eliminate the pain points in room searching by creating a direct, transparent, and broker-free platform 
                        where students and working professionals can find their perfect accommodation without hassle or hidden costs.
                    </p>
                </div>

                <div class="bg-white rounded-2xl shadow-xl p-8">
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-secondary bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-eye text-2xl text-secondary"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900">Our Vision</h3>
                    </div>
                    <p class="text-gray-600 text-lg leading-relaxed text-center">
                        To become India's most trusted platform for room rentals, where every student can find a home away from home 
                        with just a few clicks, and every property owner can list for free without any middlemen.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="py-20 bg-gradient-to-br from-primary to-purple-600 text-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">Join Our Journey</h2>
            <p class="text-xl text-purple-100 mb-8">
                We're building something special, and we'd love for you to be part of it. Whether you're looking for a room or have one to offer, 
                you're helping us create a better future for student accommodation.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/" class="px-8 py-4 bg-white text-primary rounded-xl hover:bg-gray-100 transition-colors font-semibold text-lg">
                    <i class="fas fa-search mr-2"></i>Find Your Room
                </a>
                <a href="/post-property" class="px-8 py-4 bg-secondary text-white rounded-xl hover:bg-green-700 transition-colors font-semibold text-lg">
                    <i class="fas fa-plus mr-2"></i>List Your Property
                </a>
            </div>
            
            <div class="mt-12 text-center">
                <p class="text-purple-200 text-lg">
                    💝 Made with love in Kolkata, for students everywhere
                </p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-r from-purple-900 to-indigo-900 text-white py-12">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-3 gap-8">
                <!-- About Section -->
                <div>
                    <h3 class="text-2xl font-bold mb-4 text-yellow-300">HavenHuts</h3>
                    <p class="text-gray-300 mb-4">
                        Your trusted partner for finding the perfect room rental. Built by students, for students.
                    </p>
                    <div class="flex items-center text-yellow-300">
                        <i class="fas fa-heart mr-2 text-red-400 animate-pulse"></i>
                        <span class="font-semibold">Made with love in Kolkata</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-xl font-bold mb-4 text-yellow-300">Quick Links</h4>
                    <div class="space-y-2">
                        <a href="/" class="block text-gray-300 hover:text-yellow-300 transition-colors">Home</a>
                        <a href="/about" class="block text-yellow-300 font-semibold">About Us</a>
                        <a href="/post-property" class="block text-gray-300 hover:text-yellow-300 transition-colors">Post Property</a>
                        <a href="#" class="block text-gray-300 hover:text-yellow-300 transition-colors">Contact</a>
                    </div>
                </div>

                <!-- Contact -->
                <div>
                    <h4 class="text-xl font-bold mb-4 text-yellow-300">Connect With Us</h4>
                    <div class="space-y-3">
                        <div class="flex items-center text-gray-300">
                            <i class="fas fa-envelope mr-3 text-yellow-300"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex items-center text-gray-300">
                            <i class="fas fa-map-marker-alt mr-3 text-yellow-300"></i>
                            <span>Serving Greater Kolkata Area</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-purple-700 mt-8 pt-6 text-center">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-300 text-sm">© 2024 HavenHuts. All rights reserved.</p>
                    <div class="flex items-center mt-2 md:mt-0 text-sm text-gray-300">
                        <span>Built with</span>
                        <i class="fas fa-heart mx-2 text-red-400 animate-pulse"></i>
                        <span>in Kolkata, for India</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');
            const closeMobileMenu = document.getElementById('closeMobileMenu');

            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.remove('translate-x-full');
                document.body.style.overflow = 'hidden';
            });

            closeMobileMenu.addEventListener('click', () => {
                mobileMenu.classList.add('translate-x-full');
                document.body.style.overflow = 'auto';
            });

            // Close menu when clicking outside
            mobileMenu.addEventListener('click', (e) => {
                if (e.target === mobileMenu) {
                    mobileMenu.classList.add('translate-x-full');
                    document.body.style.overflow = 'auto';
                }
            });
        });
    </script>
</body>
</html>
