#!/usr/bin/env python3
"""
Test script to verify deployment bundle creation
"""

import os
import subprocess
import sys

def test_bundle_creation():
    """Test creating the deployment bundle locally"""
    print("🧪 Testing Deployment Bundle Creation")
    print("=" * 50)
    
    # Check required files
    required_files = ['app.py', 'wsgi.py', 'requirements.txt', 'Dockerfile', 'captain-definition']
    optional_files = ['.env.example', 'sitemap.xml']
    required_dirs = ['templates']
    optional_dirs = ['static']
    
    print("📋 Checking required files...")
    missing_required = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} (MISSING)")
            missing_required.append(file)
    
    if missing_required:
        print(f"\n❌ Missing required files: {missing_required}")
        return False
    
    print("\n📋 Checking optional files...")
    for file in optional_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ⚠️  {file} (optional, missing)")
    
    print("\n📁 Checking directories...")
    missing_dirs = []
    for dir in required_dirs:
        if os.path.exists(dir) and os.path.isdir(dir):
            print(f"   ✅ {dir}/")
        else:
            print(f"   ❌ {dir}/ (MISSING)")
            missing_dirs.append(dir)
    
    for dir in optional_dirs:
        if os.path.exists(dir) and os.path.isdir(dir):
            print(f"   ✅ {dir}/")
        else:
            print(f"   ⚠️  {dir}/ (optional, missing)")
    
    if missing_dirs:
        print(f"\n❌ Missing required directories: {missing_dirs}")
        return False
    
    # Create deployment bundle
    print("\n📦 Creating deployment bundle...")
    
    try:
        # Build file list
        files_to_include = []
        
        # Add required files
        files_to_include.extend(required_files)
        
        # Add optional files that exist
        for file in optional_files:
            if os.path.exists(file):
                files_to_include.append(file)
        
        # Add directories
        for dir in required_dirs + optional_dirs:
            if os.path.exists(dir) and os.path.isdir(dir):
                files_to_include.append(dir + '/')
        
        # Create tar command
        cmd = ['tar', '-czf', 'deploy.tar'] + files_to_include
        
        print(f"📋 Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Deployment bundle created successfully!")
            
            # List contents
            list_cmd = ['tar', '-tzf', 'deploy.tar']
            list_result = subprocess.run(list_cmd, capture_output=True, text=True)
            
            if list_result.returncode == 0:
                print("\n📋 Bundle contents:")
                for line in list_result.stdout.strip().split('\n')[:20]:
                    print(f"   {line}")
                
                if len(list_result.stdout.strip().split('\n')) > 20:
                    print("   ... (truncated)")
            
            # Check file size
            if os.path.exists('deploy.tar'):
                size = os.path.getsize('deploy.tar')
                print(f"\n📊 Bundle size: {size:,} bytes ({size/1024/1024:.2f} MB)")
                
                if size > 100 * 1024 * 1024:  # 100MB
                    print("⚠️  Bundle is quite large (>100MB)")
                elif size < 1024:  # 1KB
                    print("⚠️  Bundle seems too small (<1KB)")
                else:
                    print("✅ Bundle size looks reasonable")
            
            return True
        else:
            print(f"❌ Failed to create bundle: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating bundle: {e}")
        return False
    
    finally:
        # Clean up
        if os.path.exists('deploy.tar'):
            os.remove('deploy.tar')
            print("\n🧹 Cleaned up test bundle")

def main():
    """Run the deployment bundle test"""
    success = test_bundle_creation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 DEPLOYMENT BUNDLE TEST PASSED!")
        print("\n✅ Your deployment bundle creation is working correctly")
        print("✅ GitHub Actions deployment should work")
        print("\n🚀 Ready to deploy to CapRover!")
    else:
        print("❌ DEPLOYMENT BUNDLE TEST FAILED!")
        print("\n⚠️  Fix the issues above before deploying")
        print("💡 Make sure all required files and directories exist")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
