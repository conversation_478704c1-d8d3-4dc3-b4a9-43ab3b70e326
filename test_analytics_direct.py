#!/usr/bin/env python3
"""
Direct test of analytics functionality by accessing the database directly
"""

import os
import sys
from datetime import datetime, timezone, timedelta
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_analytics_database():
    """Test analytics functionality by accessing the database directly"""
    print("Direct Analytics Database Test")
    print("=" * 40)

    try:
        # Connect to MongoDB
        client = MongoClient(os.getenv('MONGO_URI', 'mongodb://localhost:27017/'))
        db = client[os.getenv('MONGO_DB_NAME', 'eznest')]

        # Analytics collections
        analytics_sessions = db.analytics_sessions
        analytics_daily = db.analytics_daily
        analytics_events = db.analytics_events

        print("✅ Connected to MongoDB successfully")

        # Test 1: Check if analytics collections exist
        collections = db.list_collection_names()
        analytics_collections = [name for name in collections if name.startswith('analytics_')]
        print(f"✅ Analytics collections found: {analytics_collections}")

        # Test 2: Check analytics sessions
        session_count = analytics_sessions.count_documents({})
        print(f"✅ Analytics sessions count: {session_count}")

        if session_count > 0:
            # Get recent sessions
            recent_sessions = list(analytics_sessions.find({}).sort('last_activity', -1).limit(5))
            print(f"✅ Recent sessions: {len(recent_sessions)} found")

            for i, session in enumerate(recent_sessions[:3], 1):
                print(f"   Session {i}:")
                print(f"     - IP Hash: {session.get('ip_hash', 'N/A')[:8]}...")
                print(f"     - Page Views: {session.get('page_views', 0)}")
                print(f"     - Country: {session.get('country', 'Unknown')}")
                print(f"     - Last Activity: {session.get('last_activity', 'N/A')}")

        # Test 3: Check daily analytics
        daily_count = analytics_daily.count_documents({})
        print(f"✅ Daily analytics records: {daily_count}")

        if daily_count > 0:
            # Get recent daily stats
            recent_daily = list(analytics_daily.find({}).sort('date', -1).limit(3))
            print(f"✅ Recent daily stats: {len(recent_daily)} found")

            for i, daily in enumerate(recent_daily, 1):
                print(f"   Day {i}:")
                print(f"     - Date: {daily.get('date', 'N/A')}")
                print(f"     - Unique Visitors: {daily.get('unique_visitors', 0)}")
                print(f"     - Total Page Views: {daily.get('total_page_views', 0)}")

        # Test 4: Check analytics events
        events_count = analytics_events.count_documents({})
        print(f"✅ Analytics events count: {events_count}")

        if events_count > 0:
            # Get recent events
            recent_events = list(analytics_events.find({}).sort('timestamp', -1).limit(5))
            print(f"✅ Recent events: {len(recent_events)} found")

            for i, event in enumerate(recent_events[:3], 1):
                print(f"   Event {i}:")
                print(f"     - Type: {event.get('event_type', 'N/A')}")
                print(f"     - Page: {event.get('page_path', 'N/A')}")
                print(f"     - Timestamp: {event.get('timestamp', 'N/A')}")

        # Test 5: Create a test analytics session
        print("\n🧪 Testing analytics session creation...")

        test_session = {
            'ip_hash': 'test_hash_12345678',
            'session_start': datetime.now(timezone.utc),
            'last_activity': datetime.now(timezone.utc),
            'user_agent': 'Test Analytics Script',
            'first_page': '/test',
            'last_page': '/test',
            'pages_visited': ['/test'],
            'page_views': 1,
            'country': 'Test Country',
            'city': 'Test City',
            'region': 'Test Region'
        }

        result = analytics_sessions.insert_one(test_session)
        print(f"✅ Test session created with ID: {result.inserted_id}")

        # Test 6: Update daily analytics
        print("\n🧪 Testing daily analytics update...")

        today = datetime.now(timezone.utc).date()
        # Convert date to datetime for MongoDB compatibility
        today_datetime = datetime.combine(today, datetime.min.time()).replace(tzinfo=timezone.utc)
        daily_update = analytics_daily.update_one(
            {'date': today_datetime},
            {
                '$inc': {
                    'unique_visitors': 1,
                    'total_page_views': 1
                },
                '$set': {
                    'last_updated': datetime.now(timezone.utc)
                }
            },
            upsert=True
        )

        if daily_update.upserted_id:
            print(f"✅ New daily record created for {today}")
        else:
            print(f"✅ Daily record updated for {today}")

        # Test 7: Create a test event
        print("\n🧪 Testing analytics event creation...")

        test_event = {
            'ip_hash': 'test_hash_12345678',
            'event_type': 'page_view',
            'page_path': '/test',
            'timestamp': datetime.now(timezone.utc),
            'user_agent': 'Test Analytics Script'
        }

        event_result = analytics_events.insert_one(test_event)
        print(f"✅ Test event created with ID: {event_result.inserted_id}")

        # Test 8: Clean up test data
        print("\n🧹 Cleaning up test data...")

        analytics_sessions.delete_one({'_id': result.inserted_id})
        analytics_events.delete_one({'_id': event_result.inserted_id})
        print("✅ Test data cleaned up")

        print("\n🎉 All analytics database tests passed!")
        print("\n✅ Analytics System Status:")
        print(f"   - Sessions Collection: Working ({session_count} records)")
        print(f"   - Daily Analytics: Working ({daily_count} records)")
        print(f"   - Events Collection: Working ({events_count} records)")
        print(f"   - Database Operations: All successful")

        return True

    except Exception as e:
        print(f"❌ Error testing analytics database: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_analytics_indexes():
    """Test if analytics indexes are properly created"""
    print("\n🔍 Testing Analytics Indexes...")

    try:
        client = MongoClient(os.getenv('MONGO_URI', 'mongodb://localhost:27017/'))
        db = client[os.getenv('MONGO_DB_NAME', 'eznest')]

        # Check indexes for each collection
        collections = {
            'analytics_sessions': db.analytics_sessions,
            'analytics_daily': db.analytics_daily,
            'analytics_events': db.analytics_events
        }

        for name, collection in collections.items():
            indexes = list(collection.list_indexes())
            print(f"✅ {name} indexes: {len(indexes)} found")
            for idx in indexes:
                print(f"   - {idx.get('name', 'unnamed')}: {idx.get('key', {})}")

        return True

    except Exception as e:
        print(f"❌ Error checking indexes: {e}")
        return False

def main():
    """Main test function"""
    print("HavenHuts Analytics System - Direct Database Test")
    print("=" * 60)
    print(f"Started at: {datetime.now()}")
    print()

    # Test database functionality
    db_test_passed = test_analytics_database()

    # Test indexes
    index_test_passed = test_analytics_indexes()

    print("\n" + "=" * 60)
    if db_test_passed and index_test_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ Analytics system is working correctly at the database level")
        print("✅ The issue with the web server is likely related to HTTPS redirects")
        print("✅ Analytics functionality itself is intact and operational")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

    print(f"\nCompleted at: {datetime.now()}")

if __name__ == "__main__":
    main()
