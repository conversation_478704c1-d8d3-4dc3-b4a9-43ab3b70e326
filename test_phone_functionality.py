#!/usr/bin/env python3
"""
Test the improved phone number functionality
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_phone_functionality():
    """Test phone number handling with different formats"""
    
    print("📱 Phone Number Functionality Test")
    print("=" * 50)
    
    # Test cases for phone number formatting
    test_cases = [
        {
            'name': 'Simple 10-digit number',
            'input_phone': '9876543210',
            'expected_format': '+91 9876543210'
        },
        {
            'name': 'Number with +91 prefix',
            'input_phone': '+91 9876543210',
            'expected_format': '+91 9876543210'
        },
        {
            'name': 'Number with +91 no space',
            'input_phone': '+919876543210',
            'expected_format': '+91 9876543210'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        print(f"   Input: {test_case['input_phone']}")
        
        # Create test property with different phone formats
        property_data = {
            'title': f'Test Property {i} - Phone Format Test',
            'price': '15000',
            'city': 'Kolkata',
            'phone': test_case['input_phone'],
            'description': f'Test property for phone format testing - case {i}'
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/properties", data=property_data)
            
            if response.status_code == 201:
                result = response.json()
                if result.get('success'):
                    property_id = result.get('property_id')
                    edit_hash = result.get('edit_hash')
                    
                    print(f"   ✅ Property created: {property_id}")
                    
                    # Retrieve the property to check how phone was stored
                    manage_response = requests.post(f"{BASE_URL}/api/manage-property", 
                                                  json={'edit_hash': edit_hash})
                    
                    if manage_response.status_code == 200:
                        manage_result = manage_response.json()
                        if manage_result.get('success'):
                            stored_phone = manage_result.get('property', {}).get('phone', '')
                            print(f"   📞 Stored as: {stored_phone}")
                            
                            # Check if it matches expected format
                            if stored_phone == test_case['expected_format']:
                                print(f"   ✅ Format correct!")
                            else:
                                print(f"   ❌ Format incorrect! Expected: {test_case['expected_format']}")
                            
                            # Clean up - delete the test property
                            delete_response = requests.delete(f"{BASE_URL}/api/properties/{property_id}?hash={edit_hash}")
                            if delete_response.status_code == 200:
                                print(f"   🗑️  Test property cleaned up")
                        else:
                            print(f"   ❌ Failed to retrieve property: {manage_result.get('error')}")
                    else:
                        print(f"   ❌ Failed to retrieve property: {manage_response.status_code}")
                else:
                    print(f"   ❌ Failed to create property: {result.get('error')}")
            else:
                print(f"   ❌ HTTP error: {response.status_code}")
                if response.text:
                    try:
                        error_data = response.json()
                        print(f"   Error: {error_data.get('error', 'Unknown error')}")
                    except:
                        print(f"   Response: {response.text[:100]}")
                        
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    # Test frontend page loading
    print(f"\n4. Testing Frontend Page...")
    try:
        response = requests.get(f"{BASE_URL}/post-property")
        if response.status_code == 200:
            content = response.text
            
            # Check for phone number improvements
            checks = [
                ('Phone input with +91 prefix', 'pl-16' in content and '+91' in content),
                ('10-digit maxlength', 'maxlength="10"' in content),
                ('Number pattern validation', 'pattern="[0-9]{10}"' in content),
                ('User-friendly placeholder', '98765 43210' in content),
                ('Helpful instruction text', 'Just enter your 10-digit mobile number' in content)
            ]
            
            print("   Frontend improvements:")
            for check_name, check_result in checks:
                status = "✅" if check_result else "❌"
                print(f"   {status} {check_name}")
                
        else:
            print(f"   ❌ Failed to load page: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception loading page: {e}")
    
    print(f"\n🎉 Phone Number Functionality Test Complete!")
    print("📱 Key Improvements:")
    print("   ✅ Visual +91 prefix in input field")
    print("   ✅ Automatic +91 formatting on submission")
    print("   ✅ 10-digit validation with visual feedback")
    print("   ✅ Simple user experience - just type numbers")
    print("   ✅ Proper handling in edit mode")
    print("\n💡 Users can now simply type: 9876543210")
    print("   And it automatically becomes: +91 9876543210")

if __name__ == "__main__":
    test_phone_functionality()
