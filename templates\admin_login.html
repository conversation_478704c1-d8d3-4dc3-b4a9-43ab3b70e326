<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - HavenHuts</title>
    <!-- Fallback CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">

    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome with fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          onerror="this.onerror=null;this.href='https://use.fontawesome.com/releases/v6.4.0/css/all.css';">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full space-y-8 p-8">
        <div class="text-center">
            <a href="/" class="text-3xl font-bold text-primary">
                <i class="fas fa-shield-alt mr-2"></i>HavenHuts Admin
            </a>
            <h2 class="mt-6 text-2xl font-bold text-gray-900">
                Sign in to admin panel
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                Enter your admin credentials to continue
            </p>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <form id="loginForm" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700">
                        Username
                    </label>
                    <div class="mt-1 relative">
                        <input id="username" name="username" type="text" required
                               class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
                               placeholder="Enter username">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-user text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">
                        Password
                    </label>
                    <div class="mt-1 relative">
                        <input id="password" name="password" type="password" required
                               class="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
                               placeholder="Enter password">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    <span id="errorText">Invalid credentials</span>
                </div>

                <div>
                    <button type="submit" id="loginButton"
                            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-sign-in-alt text-purple-300 group-hover:text-purple-200"></i>
                        </span>
                        <span id="loginButtonText">Sign in</span>
                    </button>
                </div>
            </form>
        </div>

        <div class="text-center">
            <a href="/" class="text-sm text-primary hover:text-purple-700">
                <i class="fas fa-arrow-left mr-1"></i>Back to website
            </a>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');
            const loginButton = document.getElementById('loginButton');
            const loginButtonText = document.getElementById('loginButtonText');

            // Show loading state
            loginButton.disabled = true;
            loginButtonText.textContent = 'Signing in...';
            errorMessage.classList.add('hidden');

            try {
                // Send login request to the new login endpoint
                const response = await fetch('/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    // Redirect to admin panel
                    window.location.href = result.redirect || '/admin/panel';
                } else {
                    // Show error
                    errorMessage.classList.remove('hidden');
                    document.getElementById('errorText').textContent = result.error || 'Invalid username or password';
                }
            } catch (error) {
                console.error('Login error:', error);
                errorMessage.classList.remove('hidden');
                document.getElementById('errorText').textContent = 'Login failed. Please try again.';
            } finally {
                // Reset button state
                loginButton.disabled = false;
                loginButtonText.textContent = 'Sign in';
            }
        });

        // Auto-focus username field
        document.getElementById('username').focus();
    </script>
</body>
</html>
