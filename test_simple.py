#!/usr/bin/env python3
import requests

BASE_URL = "http://localhost:5000"

# Login first
session = requests.Session()
login_data = {'username': 'admin', 'password': 'Interpreter@1435'}

print("Logging in...")
login_response = session.post(f"{BASE_URL}/admin/login", json=login_data)
print(f"Login status: {login_response.status_code}")

# Test large page size
print("\nTesting large page size...")
params = {'admin': 'true', 'page': 1, 'per_page': 25}
response = session.get(f"{BASE_URL}/api/properties", params=params)
print(f"Status: {response.status_code}")
print(f"Response: {response.text}")
