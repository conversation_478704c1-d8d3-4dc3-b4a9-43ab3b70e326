#!/usr/bin/env python3
"""
Test the new simplified admin endpoint
"""

import requests

BASE_URL = "http://localhost:5000"

def test_new_endpoint():
    """Test the new simplified admin endpoint"""
    
    # Login first
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'Interpreter@1435'}
    
    print("Testing new simplified admin endpoint...")
    login_response = session.post(f"{BASE_URL}/admin/login", json=login_data)
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.status_code}")
        return
    
    print("Login successful!")
    
    # Test the new endpoint with different page sizes
    test_cases = [
        {'per_page': 5, 'name': 'Small page (5)'},
        {'per_page': 10, 'name': 'Medium page (10)'},
        {'per_page': 25, 'name': 'Large page (25)'},
        {'per_page': 50, 'name': 'Extra large page (50)'},
    ]
    
    for test_case in test_cases:
        print(f"\nTesting: {test_case['name']}")
        params = {
            'page': 1,
            'per_page': test_case['per_page']
        }
        
        try:
            response = session.get(f"{BASE_URL}/api/admin/properties", params=params)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    properties = data.get('data', [])
                    print(f"✅ Success: {len(properties)} properties loaded")
                    print(f"Total: {data.get('total', 0)}, Page: {data.get('page', 1)}/{data.get('pages', 1)}")
                    print(f"Has next: {data.get('has_next', False)}, Has prev: {data.get('has_prev', False)}")
                    
                    # Check if properties have the expected fields
                    if properties:
                        sample = properties[0]
                        print(f"Sample property fields: {list(sample.keys())[:10]}...")  # Show first 10 fields
                        print(f"Sample price: {sample.get('price', 'N/A')}")
                        print(f"Sample city: {sample.get('city', 'N/A')}")
                        print(f"Sample status: {sample.get('status', 'N/A')}")
                else:
                    print(f"❌ API Error: {data.get('error')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_new_endpoint()
