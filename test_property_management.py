#!/usr/bin/env python3
"""
Comprehensive test for the unified property management functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_property_management():
    """Test the complete property management workflow"""
    
    print("🏠 HavenHuts - Property Management Test Suite")
    print("=" * 60)
    
    # Test 1: Create a new property and get edit hash
    print("\n1. Testing Property Creation with Edit Hash...")
    
    # Prepare test property data
    property_data = {
        'title': 'Test Property for Management',
        'price': '15000',
        'city': 'Kolkata',
        'phone': '+91 98765 43210',
        'address': '123 Test Street, Salt Lake',
        'description': 'This is a test property for management functionality testing.',
        'bedrooms': '2',
        'bathrooms': '1',
        'furnished_status': 'semi-furnished',
        'amenities': json.dumps(['wifi', 'parking'])
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/properties", data=property_data)
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                property_id = result.get('property_id')
                edit_hash = result.get('edit_hash')
                
                print(f"✅ Property created successfully!")
                print(f"   Property ID: {property_id}")
                print(f"   Edit Hash: {edit_hash[:20]}...")
                
                # Test 2: Retrieve property using edit hash
                print("\n2. Testing Property Retrieval with Edit Hash...")
                
                manage_response = requests.post(f"{BASE_URL}/api/manage-property", 
                                              json={'edit_hash': edit_hash})
                
                if manage_response.status_code == 200:
                    manage_result = manage_response.json()
                    if manage_result.get('success'):
                        retrieved_property = manage_result.get('property')
                        print("✅ Property retrieved successfully!")
                        print(f"   Title: {retrieved_property.get('title')}")
                        print(f"   Price: ₹{retrieved_property.get('price')}")
                        print(f"   City: {retrieved_property.get('city')}")
                    else:
                        print(f"❌ Failed to retrieve property: {manage_result.get('error')}")
                        return False
                else:
                    print(f"❌ HTTP error retrieving property: {manage_response.status_code}")
                    return False
                
                # Test 3: Update property using edit hash
                print("\n3. Testing Property Update...")
                
                update_data = {
                    'title': 'Updated Test Property for Management',
                    'price': '18000',
                    'city': 'Kolkata',
                    'phone': '+91 98765 43210',
                    'address': '123 Updated Test Street, Salt Lake',
                    'description': 'This is an updated test property for management functionality testing.',
                    'bedrooms': '3',
                    'bathrooms': '2',
                    'furnished_status': 'fully-furnished',
                    'amenities': json.dumps(['wifi', 'parking', 'gym'])
                }
                
                update_response = requests.put(f"{BASE_URL}/api/properties/{property_id}?hash={edit_hash}", 
                                             data=update_data)
                
                if update_response.status_code == 200:
                    update_result = update_response.json()
                    if update_result.get('success'):
                        print("✅ Property updated successfully!")
                        
                        # Verify the update by retrieving again
                        verify_response = requests.post(f"{BASE_URL}/api/manage-property", 
                                                      json={'edit_hash': edit_hash})
                        
                        if verify_response.status_code == 200:
                            verify_result = verify_response.json()
                            if verify_result.get('success'):
                                updated_property = verify_result.get('property')
                                print(f"   Updated Title: {updated_property.get('title')}")
                                print(f"   Updated Price: ₹{updated_property.get('price')}")
                                print(f"   Updated Bedrooms: {updated_property.get('bedrooms')}")
                            else:
                                print(f"❌ Failed to verify update: {verify_result.get('error')}")
                    else:
                        print(f"❌ Failed to update property: {update_result.get('error')}")
                        return False
                else:
                    print(f"❌ HTTP error updating property: {update_response.status_code}")
                    return False
                
                # Test 4: Test invalid edit hash
                print("\n4. Testing Invalid Edit Hash...")
                
                invalid_response = requests.post(f"{BASE_URL}/api/manage-property", 
                                               json={'edit_hash': 'invalid_hash_123'})
                
                if invalid_response.status_code == 404:
                    invalid_result = invalid_response.json()
                    if not invalid_result.get('success'):
                        print("✅ Invalid hash properly rejected!")
                        print(f"   Error message: {invalid_result.get('error')}")
                    else:
                        print("❌ Invalid hash should have been rejected")
                else:
                    print(f"❌ Unexpected response for invalid hash: {invalid_response.status_code}")
                
                # Test 5: Delete property using edit hash
                print("\n5. Testing Property Deletion...")
                
                delete_response = requests.delete(f"{BASE_URL}/api/properties/{property_id}?hash={edit_hash}")
                
                if delete_response.status_code == 200:
                    delete_result = delete_response.json()
                    if delete_result.get('success'):
                        print("✅ Property deleted successfully!")
                        
                        # Verify deletion by trying to retrieve
                        verify_delete_response = requests.post(f"{BASE_URL}/api/manage-property", 
                                                             json={'edit_hash': edit_hash})
                        
                        if verify_delete_response.status_code == 404:
                            print("✅ Property properly removed from database!")
                        else:
                            print("❌ Property still exists after deletion")
                    else:
                        print(f"❌ Failed to delete property: {delete_result.get('error')}")
                        return False
                else:
                    print(f"❌ HTTP error deleting property: {delete_response.status_code}")
                    return False
                
            else:
                print(f"❌ Failed to create property: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP error creating property: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception during testing: {e}")
        return False
    
    # Test 6: Test rate limiting
    print("\n6. Testing Rate Limiting...")
    
    try:
        # Make multiple rapid requests to test rate limiting
        for i in range(12):  # Exceed the 10 per minute limit
            response = requests.post(f"{BASE_URL}/api/manage-property", 
                                   json={'edit_hash': 'test_hash'})
            if response.status_code == 429:
                print(f"✅ Rate limiting working! Blocked after {i+1} requests")
                break
        else:
            print("⚠️  Rate limiting may not be working as expected")
    except Exception as e:
        print(f"⚠️  Rate limiting test failed: {e}")
    
    print("\n🎉 Property Management Test Suite Complete!")
    print("✅ All core functionality is working properly")
    print("🔐 Edit hash system provides secure property management")
    print("🔄 CRUD operations (Create, Read, Update, Delete) all functional")
    print("🛡️  Rate limiting and validation working correctly")
    
    return True

if __name__ == "__main__":
    test_property_management()
