#!/usr/bin/env python3
"""
Test script for the admin API endpoints to verify smart loading functionality
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_admin_properties_api():
    """Test the admin properties API with pagination"""
    print("🧪 Testing Admin Properties API with Smart Loading...")

    # Test session-based authentication (simulate logged-in admin)
    session = requests.Session()

    # First, login to get session
    login_data = {
        'username': 'admin',
        'password': 'Interpreter@1435'
    }

    print("1. Testing admin login...")
    login_response = session.post(f"{BASE_URL}/admin/login", json=login_data)

    if login_response.status_code == 200:
        login_result = login_response.json()
        if login_result.get('success'):
            print("✅ Admin login successful")
        else:
            print(f"❌ Admin login failed: {login_result.get('error')}")
            return False
    else:
        print(f"❌ Admin login HTTP error: {login_response.status_code}")
        return False

    # Test paginated properties loading
    print("\n2. Testing paginated properties loading...")

    test_cases = [
        {"page": 1, "per_page": 10, "description": "First page with 10 items"},
        {"page": 1, "per_page": 25, "description": "First page with 25 items"},
        {"page": 1, "per_page": 5, "status": "pending", "description": "Pending properties only"},
        {"page": 1, "per_page": 10, "search": "kolkata", "description": "Search for 'kolkata'"},
        {"page": 1, "per_page": 10, "sort_by": "price_desc", "description": "Sort by price descending"},
    ]

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n   Test {i}: {test_case['description']}")

        # Build parameters
        params = {
            'admin': 'true',
            'page': test_case.get('page', 1),
            'per_page': test_case.get('per_page', 10)
        }

        # Add optional parameters
        if 'status' in test_case:
            params['status'] = test_case['status']
        if 'search' in test_case:
            params['search'] = test_case['search']
        if 'sort_by' in test_case:
            params['sort_by'] = test_case['sort_by']

        start_time = time.time()
        response = session.get(f"{BASE_URL}/api/properties", params=params)
        end_time = time.time()

        response_time = (end_time - start_time) * 1000  # Convert to milliseconds

        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    properties = data.get('data', [])
                    total = data.get('total', 0)
                    page = data.get('page', 1)
                    pages = data.get('pages', 1)
                    has_next = data.get('has_next', False)
                    has_prev = data.get('has_prev', False)

                    print(f"   ✅ Success: {len(properties)} properties loaded")
                    print(f"   📊 Total: {total}, Page: {page}/{pages}")
                    print(f"   ⏱️  Response time: {response_time:.1f}ms")
                    print(f"   🔄 Has next: {has_next}, Has prev: {has_prev}")

                    # Check if response time is reasonable (under 2 seconds)
                    if response_time > 2000:
                        print(f"   ⚠️  Warning: Slow response time ({response_time:.1f}ms)")
                    else:
                        print(f"   ⚡ Good response time")

                else:
                    print(f"   ❌ API error: {data.get('error')}")
            except json.JSONDecodeError:
                print(f"   ❌ Invalid JSON response")
        else:
            print(f"   ❌ HTTP error: {response.status_code}")
            if response.status_code == 403:
                print("   🔒 Authentication issue - admin access required")

    print("\n3. Testing dashboard stats...")
    stats_response = session.get(f"{BASE_URL}/admin")

    if stats_response.status_code == 200:
        try:
            stats_data = stats_response.json()
            if stats_data.get('success'):
                stats = stats_data.get('stats', {})
                print(f"   ✅ Dashboard stats loaded:")
                print(f"   📈 Total: {stats.get('total', 0)}")
                print(f"   ⏳ Pending: {stats.get('pending', 0)}")
                print(f"   ✅ Approved: {stats.get('approved', 0)}")
                print(f"   🚩 Reported: {stats.get('reported', 0)}")
            else:
                print(f"   ❌ Stats error: {stats_data.get('error')}")
        except json.JSONDecodeError:
            print(f"   ❌ Invalid JSON response for stats")
    else:
        print(f"   ❌ Stats HTTP error: {stats_response.status_code}")

    return True

def test_performance():
    """Test performance with different page sizes"""
    print("\n🚀 Performance Testing...")

    session = requests.Session()

    # Login first
    login_data = {'username': 'admin', 'password': 'Interpreter@1435'}
    session.post(f"{BASE_URL}/admin/login", json=login_data)

    page_sizes = [5, 10, 25, 50, 100]

    for page_size in page_sizes:
        params = {
            'admin': 'true',
            'page': 1,
            'per_page': page_size
        }

        start_time = time.time()
        response = session.get(f"{BASE_URL}/api/properties", params=params)
        end_time = time.time()

        response_time = (end_time - start_time) * 1000

        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                count = len(data.get('data', []))
                print(f"   📄 Page size {page_size}: {count} items in {response_time:.1f}ms")
            else:
                print(f"   ❌ Error with page size {page_size}: {data.get('error')}")
        else:
            print(f"   ❌ HTTP error with page size {page_size}: {response.status_code}")

if __name__ == "__main__":
    print("🏠 HavenHuts Admin API Test Suite")
    print("=" * 50)

    try:
        success = test_admin_properties_api()
        if success:
            test_performance()
            print("\n🎉 All tests completed!")
        else:
            print("\n❌ Tests failed!")
    except Exception as e:
        print(f"\n💥 Test suite error: {e}")
