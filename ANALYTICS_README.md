# HavenHuts Analytics System

A comprehensive, privacy-compliant analytics system for the HavenHuts property rental platform.

## Features

### 🔍 **Visitor Tracking**
- **Privacy-First**: IP addresses are hashed with salt for privacy compliance
- **Session Management**: Automatic session tracking with 30-minute timeout
- **Page View Tracking**: Tracks all page visits with timestamps
- **Geographic Data**: Basic location tracking using IP geolocation
- **User Agent Analysis**: Browser and device information

### 📊 **Real-Time Analytics Dashboard**
- **Live Visitor Count**: See active visitors in real-time
- **Today's Statistics**: Current day's unique visitors and page views
- **Historical Data**: All-time visitor and page view totals
- **Real-Time Updates**: WebSocket-powered live updates

### 📈 **Data Visualization**
- **Daily Trends Chart**: 30-day visitor and page view trends using Chart.js
- **Geographic Distribution**: Top countries and cities with progress bars
- **Recent Visitors**: Live feed of recent visitor sessions
- **Time-based Analysis**: Visitor activity patterns

### 📋 **Admin Panel Integration**
- **Dedicated Analytics Tab**: Integrated into existing admin panel
- **Export Functionality**: CSV export for daily stats and session data
- **Filtering Options**: Date range and data type filters
- **Responsive Design**: Works on desktop and mobile devices

### 🔒 **Privacy & Security**
- **GDPR Compliant**: IP hashing and data anonymization
- **Admin-Only Access**: Analytics restricted to authenticated admins
- **Secure API**: All analytics endpoints require admin authentication
- **Data Retention**: Configurable data retention policies

## Technical Implementation

### Database Collections

#### `analytics_sessions`
```javascript
{
  ip_hash: "hashed_ip_address",
  session_start: ISODate,
  last_activity: ISODate,
  user_agent: "browser_info",
  first_page: "/landing_page",
  last_page: "/exit_page",
  pages_visited: ["/page1", "/page2"],
  page_views: 5,
  country: "India",
  city: "Kolkata",
  region: "West Bengal"
}
```

#### `analytics_daily`
```javascript
{
  date: ISODate,
  unique_visitors: 150,
  total_page_views: 450,
  updated_at: ISODate
}
```

#### `analytics_events`
```javascript
{
  session_id: ObjectId,
  ip_hash: "hashed_ip",
  timestamp: ISODate,
  event_type: "page_view",
  page_path: "/property/123",
  user_agent: "browser_info"
}
```

### API Endpoints

#### Analytics Summary
```
GET /api/analytics/summary
```
Returns real-time analytics overview.

#### Daily Analytics
```
GET /api/analytics/daily?days=30
```
Returns daily visitor statistics for specified number of days.

#### Geographic Analytics
```
GET /api/analytics/geographic
```
Returns visitor distribution by country and city.

#### Recent Visitors
```
GET /api/analytics/visitors?limit=50
```
Returns recent visitor sessions.

#### Export Data
```
GET /api/analytics/export?type=daily
GET /api/analytics/export?type=sessions
```
Exports analytics data as CSV files.

### Real-Time Features

#### WebSocket Events
- `join_admin`: Admin joins analytics room
- `leave_admin`: Admin leaves analytics room
- `analytics_update`: Real-time analytics updates

#### Automatic Tracking
- Middleware automatically tracks all page visits
- Excludes static files, admin pages, and API calls
- Updates session data and daily statistics
- Emits real-time updates to connected admins

## Installation & Setup

### 1. Install Dependencies
```bash
pip install flask-socketio requests
```

### 2. Environment Variables
Add to your `.env` file:
```env
ANALYTICS_SALT=your-unique-salt-for-ip-hashing
```

### 3. Database Indexes
The system automatically creates necessary MongoDB indexes:
- Session tracking indexes
- Daily statistics indexes
- Event logging indexes

### 4. Admin Panel Access
Navigate to `/admin` and click the "Analytics" tab to view the dashboard.

## Usage

### Viewing Analytics
1. Login to admin panel: `http://localhost:5000/admin`
2. Click the "Analytics" tab
3. View real-time statistics and charts
4. Export data using the export buttons

### Testing the System
Run the test script to simulate visitors:
```bash
python test_analytics.py
```

### Monitoring Performance
- Analytics tracking is lightweight and non-blocking
- Uses background processing for geolocation lookups
- Implements efficient database queries with proper indexing
- Real-time updates use WebSocket for minimal overhead

## Privacy Compliance

### Data Protection
- **IP Hashing**: All IP addresses are hashed with salt
- **No Personal Data**: No personally identifiable information stored
- **Anonymized Sessions**: Sessions identified by hash only
- **Configurable Retention**: Easy to implement data retention policies

### GDPR Compliance
- Data minimization: Only essential analytics data collected
- Purpose limitation: Data used only for website analytics
- Transparency: Clear data collection practices
- User rights: Easy to implement data deletion requests

## Customization

### Adding Custom Events
```python
# Track custom events
analytics_events.insert_one({
    'session_id': session_id,
    'ip_hash': ip_hash,
    'timestamp': datetime.now(timezone.utc),
    'event_type': 'property_view',
    'page_path': f'/property/{property_id}',
    'metadata': {'property_id': property_id}
})
```

### Custom Analytics Queries
```python
# Example: Get most popular pages
popular_pages = analytics_events.aggregate([
    {'$match': {'event_type': 'page_view'}},
    {'$group': {'_id': '$page_path', 'views': {'$sum': 1}}},
    {'$sort': {'views': -1}},
    {'$limit': 10}
])
```

## Performance Considerations

### Optimization Tips
- Analytics tracking runs asynchronously
- Database queries use proper indexing
- Real-time updates are throttled to prevent spam
- Geolocation lookups have timeout limits
- Failed operations fail silently to not affect user experience

### Scaling
- Consider using Redis for session caching in high-traffic scenarios
- Implement data archiving for long-term storage
- Use database sharding for very large datasets
- Consider CDN for static analytics assets

## Troubleshooting

### Common Issues
1. **No analytics data**: Check if middleware is properly installed
2. **Real-time updates not working**: Verify SocketIO is properly configured
3. **Geolocation not working**: Check internet connectivity and API limits
4. **Export not working**: Verify admin authentication and file permissions

### Debug Mode
Enable debug logging to troubleshoot issues:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Future Enhancements

### Planned Features
- **Conversion Tracking**: Track property inquiries and bookings
- **A/B Testing**: Built-in A/B testing framework
- **Custom Dashboards**: User-configurable analytics dashboards
- **API Rate Limiting**: Advanced rate limiting based on analytics data
- **Predictive Analytics**: ML-powered visitor behavior prediction
- **Mobile App Analytics**: Support for mobile app tracking

### Integration Opportunities
- **Google Analytics**: Dual tracking with GA4
- **Marketing Tools**: Integration with email marketing platforms
- **CRM Systems**: Visitor data export to CRM systems
- **Business Intelligence**: Integration with BI tools like Tableau

## Support

For questions or issues with the analytics system:
1. Check the troubleshooting section above
2. Review the application logs for error messages
3. Test with the provided test script
4. Verify all dependencies are properly installed

The analytics system is designed to be robust, privacy-compliant, and easy to use while providing valuable insights into your HavenHuts platform usage.
