<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HavenHuts - Find Your Perfect Room Rental | Premium Accommodations</title>
    <meta name="description" content="Discover premium room rentals and accommodations on HavenHuts. Browse verified properties with detailed photos, amenities, and instant WhatsApp contact. Find your perfect home today.">
    <meta name="keywords" content="room rental, accommodation, apartment, housing, rent, property, HavenHuts">
    <meta name="author" content="HavenHuts">
    <meta property="og:title" content="HavenHuts - Find Your Perfect Room Rental">
    <meta property="og:description" content="Discover premium room rentals and accommodations with verified properties and instant contact.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://havenhuts.com">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://havenhuts.com">

    <!-- Fallback CSS for when Tailwind doesn't load -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">

    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome with fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          onerror="this.onerror=null;this.href='https://use.fontawesome.com/releases/v6.4.0/css/all.css';">

    <!-- Font Awesome detection and fallback script -->
    <script>
        // Check if Font Awesome loaded properly
        document.addEventListener('DOMContentLoaded', function() {
            const testIcon = document.createElement('i');
            testIcon.className = 'fas fa-home';
            testIcon.style.position = 'absolute';
            testIcon.style.left = '-9999px';
            document.body.appendChild(testIcon);

            setTimeout(() => {
                const computedStyle = window.getComputedStyle(testIcon, '::before');
                const content = computedStyle.getPropertyValue('content');

                // If Font Awesome didn't load, add fallback class
                if (!content || content === 'none' || content === '""') {
                    document.body.classList.add('fa-fallback');
                    console.log('Font Awesome fallback activated');
                }

                document.body.removeChild(testIcon);
            }, 100);
        });
    </script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <style>
        .property-card {
            transition: all 0.3s ease;
        }
        .property-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .modal-overlay {
            backdrop-filter: blur(4px);
        }
        .scrollable-description {
            max-height: 120px;
            overflow-y: auto;
        }
        .scrollable-description::-webkit-scrollbar {
            width: 4px;
        }
        .scrollable-description::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }
        .scrollable-description::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
        .mobile-menu {
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        .mobile-menu.open {
            transform: translateX(0);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-primary">
                        <i class="fas fa-home mr-2"></i>HavenHuts
                    </h1>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/" class="text-gray-700 hover:text-primary transition-colors font-medium">Home</a>
                    <a href="/post-property" class="text-gray-700 hover:text-primary transition-colors font-medium">Post Property</a>
                    <a href="#" class="text-gray-700 hover:text-primary transition-colors font-medium">About</a>
                    <a href="#" class="text-gray-700 hover:text-primary transition-colors font-medium">Contact</a>
                </nav>

                <!-- Mobile Menu Button -->
                <button id="mobileMenuBtn" class="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors">
                    <i class="fas fa-bars text-gray-700 text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobileMenu" class="mobile-menu fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-50 md:hidden">
            <div class="p-6">
                <div class="flex justify-between items-center mb-8">
                    <h2 class="text-xl font-bold text-primary">Menu</h2>
                    <button id="closeMobileMenu" class="p-2 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-times text-gray-700 text-xl"></i>
                    </button>
                </div>
                <nav class="space-y-4">
                    <a href="/" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-home mr-3"></i>Home
                    </a>
                    <a href="/post-property" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-plus mr-3"></i>Post Property
                    </a>
                    <a href="#" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-info-circle mr-3"></i>About
                    </a>
                    <a href="#" class="block py-3 px-4 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors font-medium">
                        <i class="fas fa-envelope mr-3"></i>Contact
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-primary to-purple-600 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">Find Your Perfect Room</h1>
            <p class="text-xl md:text-2xl mb-8 text-purple-100">Discover comfortable and affordable accommodations</p>

            <!-- Search Bar -->
            <div class="max-w-4xl mx-auto bg-white rounded-2xl p-6 shadow-2xl">
                <div class="flex flex-col lg:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            <input type="text" id="searchInput" placeholder="Search by location, title..."
                                   class="w-full pl-12 pr-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary focus:border-transparent text-gray-900 text-lg">
                        </div>
                    </div>
                    <button onclick="searchProperties()"
                            class="px-8 py-4 bg-primary text-white rounded-xl hover:bg-purple-700 transition-colors font-semibold text-lg">
                        Search Properties
                    </button>
                </div>

                <!-- Filters -->
                <div class="flex flex-wrap gap-4 justify-center mt-6">
                    <select id="priceFilter" onchange="filterProperties()"
                            class="px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary text-gray-900">
                        <option value="recommended">Recommended</option>
                        <option value="low-to-high">Price: Low to High</option>
                        <option value="high-to-low">Price: High to Low</option>
                    </select>
                    <select id="locationFilter" onchange="filterProperties()"
                            class="px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary text-gray-900">
                        <option value="">All Locations</option>
                        <option value="salt lake">Salt Lake</option>
                        <option value="park street">Park Street</option>
                        <option value="ballygunge">Ballygunge</option>
                        <option value="new town">New Town</option>
                        <option value="howrah">Howrah</option>
                        <option value="jadavpur">Jadavpur</option>
                        <option value="tollygunge">Tollygunge</option>
                        <option value="gariahat">Gariahat</option>
                        <option value="alipore">Alipore</option>
                        <option value="behala">Behala</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Free Listing Advertisement & Broker-Free Platform Banner -->
    <section class="py-12 bg-gradient-to-r from-green-50 to-blue-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <!-- Free Listing Ad -->
                <div class="bg-white rounded-2xl p-8 shadow-xl border-2 border-green-200 hover:shadow-2xl transition-all duration-300">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
                            <i class="fas fa-plus-circle text-3xl text-green-600"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-3">List Your Property for FREE!</h3>
                        <p class="text-gray-600 mb-6 text-lg">Join thousands of property owners who trust HavenHuts. No hidden charges, no commission fees!</p>

                        <div class="space-y-3 mb-6">
                            <div class="flex items-center justify-center text-green-600">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span class="font-medium">100% Free Listing</span>
                            </div>
                            <div class="flex items-center justify-center text-green-600">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span class="font-medium">Instant WhatsApp Leads</span>
                            </div>
                            <div class="flex items-center justify-center text-green-600">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span class="font-medium">No Commission Fees</span>
                            </div>
                        </div>

                        <a href="/post-property"
                           class="inline-block w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-4 px-8 rounded-xl hover:from-green-600 hover:to-green-700 transition-all duration-300 font-bold text-lg shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                            <i class="fas fa-home mr-2"></i>Post Your Property Now
                        </a>

                        <p class="text-sm text-gray-500 mt-3">Takes less than 2 minutes!</p>
                    </div>
                </div>

                <!-- Broker-Free Platform Banner -->
                <div class="bg-gradient-to-br from-blue-600 to-purple-700 rounded-2xl p-8 text-white shadow-xl">
                    <div class="text-center">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-20 rounded-full mb-4">
                            <i class="fas fa-handshake text-3xl text-white"></i>
                        </div>
                        <h3 class="text-3xl font-bold mb-4">100% Broker-Free Platform</h3>
                        <p class="text-blue-100 mb-6 text-lg">Connect directly with property owners. No middlemen, no extra charges!</p>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                            <div class="bg-white bg-opacity-10 rounded-lg p-4">
                                <i class="fas fa-user-friends text-2xl mb-2"></i>
                                <p class="font-semibold">Direct Contact</p>
                                <p class="text-sm text-blue-100">Talk directly to owners</p>
                            </div>
                            <div class="bg-white bg-opacity-10 rounded-lg p-4">
                                <i class="fas fa-money-bill-wave text-2xl mb-2"></i>
                                <p class="font-semibold">Save Money</p>
                                <p class="text-sm text-blue-100">No broker commission</p>
                            </div>
                            <div class="bg-white bg-opacity-10 rounded-lg p-4">
                                <i class="fas fa-clock text-2xl mb-2"></i>
                                <p class="font-semibold">Quick Process</p>
                                <p class="text-sm text-blue-100">Instant connections</p>
                            </div>
                            <div class="bg-white bg-opacity-10 rounded-lg p-4">
                                <i class="fas fa-shield-alt text-2xl mb-2"></i>
                                <p class="font-semibold">Verified Listings</p>
                                <p class="text-sm text-blue-100">Trusted properties</p>
                            </div>
                        </div>

                        <div class="bg-white bg-opacity-20 rounded-lg p-4">
                            <p class="font-bold text-xl">💝 Made with love in Kolkata</p>
                            <p class="text-blue-100 text-sm">For property seekers across India</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Properties Grid -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Properties</h2>
                <p class="text-xl text-gray-600">Handpicked accommodations for your comfort</p>
            </div>

            <div id="propertiesGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Property cards will be dynamically generated here -->
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="text-center py-12 hidden">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                <p class="mt-4 text-gray-600 text-lg">Loading amazing properties...</p>
            </div>

            <!-- No Results State -->
            <div id="noResultsState" class="text-center py-16 hidden">
                <i class="fas fa-search text-6xl text-gray-300 mb-6"></i>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">No Properties Found</h3>
                <p class="text-gray-600 text-lg">Try adjusting your search criteria or browse all properties.</p>
            </div>
        </div>
    </section>

    <!-- Property Details Modal -->
    <div id="propertyModal" class="fixed inset-0 bg-black bg-opacity-50 modal-overlay flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Property Details</h2>
                    <button onclick="closeModal()" class="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                        <i class="fas fa-times text-gray-500 text-xl"></i>
                    </button>
                </div>

                <div id="modalContent">
                    <!-- Modal content will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </div>



    <script>
        // Global variables
        let properties = [];
        let filteredProperties = [];

        // Format currency in Indian style (₹1,50,000)
        function formatIndianCurrency(amount) {
            // Convert to string and handle decimal places
            const numStr = Math.round(amount).toString();

            // Indian numbering system: last 3 digits, then groups of 2
            if (numStr.length <= 3) {
                return numStr;
            }

            const lastThree = numStr.substring(numStr.length - 3);
            const otherNumbers = numStr.substring(0, numStr.length - 3);

            if (otherNumbers !== '') {
                return otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + ',' + lastThree;
            } else {
                return lastThree;
            }
        }

        // Create swipeable image gallery for property cards
        function createImageGallery(property) {
            const images = property.image_urls && property.image_urls.length > 0
                ? property.image_urls
                : ['/placeholder.svg?height=300&width=400'];

            const galleryId = `gallery-${property._id}`;

            if (images.length === 1) {
                // Single image - no gallery needed
                return `
                    <img src="${images[0]}"
                         alt="${property.title} - ${property.city}"
                         class="w-full h-64 object-cover"
                         itemprop="image">
                `;
            }

            // Multiple images - create swipeable gallery with fixed width calculation
            return `
                <div class="image-gallery relative w-full h-64 overflow-hidden" id="${galleryId}" data-current-index="0" data-total-images="${images.length}">
                    <div class="gallery-container flex transition-transform duration-300 ease-in-out h-full w-full">
                        ${images.map((url, index) => `
                            <img src="${url}"
                                 alt="${property.title} - Image ${index + 1}"
                                 class="w-full h-full object-cover flex-shrink-0"
                                 ${index === 0 ? 'itemprop="image"' : ''}
                                 loading="lazy">
                        `).join('')}
                    </div>

                    <!-- Navigation arrows -->
                    <button onclick="previousImage('${property._id}')"
                            class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all z-10"
                            aria-label="Previous image">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button onclick="nextImage('${property._id}')"
                            class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all z-10"
                            aria-label="Next image">
                        <i class="fas fa-chevron-right"></i>
                    </button>

                    <!-- Image indicators -->
                    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                        ${images.map((_, index) => `
                            <button onclick="goToImage('${property._id}', ${index})"
                                    class="w-2 h-2 rounded-full transition-all indicator-dot ${index === 0 ? 'bg-white bg-opacity-100' : 'bg-white bg-opacity-50 hover:bg-opacity-75'}"
                                    data-index="${index}"
                                    aria-label="Go to image ${index + 1}"></button>
                        `).join('')}
                    </div>

                    <!-- Image counter -->
                    <div class="absolute top-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm z-10">
                        <span class="current-image">1</span> / ${images.length}
                    </div>
                </div>
            `;
        }

        // Gallery navigation functions
        function nextImage(propertyId) {
            const gallery = document.getElementById(`gallery-${propertyId}`);
            const container = gallery.querySelector('.gallery-container');
            const images = container.querySelectorAll('img');
            const indicators = gallery.querySelectorAll('.indicator-dot');
            const counter = gallery.querySelector('.current-image');

            let currentIndex = parseInt(gallery.dataset.currentIndex || '0');
            currentIndex = (currentIndex + 1) % images.length;

            updateGallery(container, indicators, counter, currentIndex, images.length);
        }

        function previousImage(propertyId) {
            const gallery = document.getElementById(`gallery-${propertyId}`);
            const container = gallery.querySelector('.gallery-container');
            const images = container.querySelectorAll('img');
            const indicators = gallery.querySelectorAll('.indicator-dot');
            const counter = gallery.querySelector('.current-image');

            let currentIndex = parseInt(gallery.dataset.currentIndex || '0');
            currentIndex = currentIndex <= 0 ? images.length - 1 : currentIndex - 1;

            updateGallery(container, indicators, counter, currentIndex, images.length);
        }

        function goToImage(propertyId, index) {
            const gallery = document.getElementById(`gallery-${propertyId}`);
            const container = gallery.querySelector('.gallery-container');
            const images = container.querySelectorAll('img');
            const indicators = gallery.querySelectorAll('.indicator-dot');
            const counter = gallery.querySelector('.current-image');

            updateGallery(container, indicators, counter, index, images.length);
        }

        function updateGallery(container, indicators, counter, currentIndex, totalImages) {
            // Store current index in gallery's parent element
            const gallery = container.closest('.image-gallery');
            if (gallery) {
                gallery.dataset.currentIndex = currentIndex;
            }

            // Update container position - each image takes 100% width
            const translateX = -(currentIndex * 100);
            container.style.transform = `translateX(${translateX}%)`;

            // Update indicators
            indicators.forEach((indicator, index) => {
                indicator.classList.remove('bg-white', 'bg-opacity-100', 'bg-opacity-50', 'bg-opacity-75');
                if (index === currentIndex) {
                    indicator.classList.add('bg-white', 'bg-opacity-100');
                } else {
                    indicator.classList.add('bg-white', 'bg-opacity-50');
                }
            });

            // Update counter
            if (counter) {
                counter.textContent = currentIndex + 1;
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            initializeMobileMenu();
            initializeSearch();
            // Load properties from backend instead of using sample data
            loadPropertiesFromBackend();
        });

        // Mobile menu functionality
        function initializeMobileMenu() {
            const mobileMenuBtn = document.getElementById('mobileMenuBtn');
            const mobileMenu = document.getElementById('mobileMenu');
            const closeMobileMenu = document.getElementById('closeMobileMenu');

            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.add('open');
                document.body.style.overflow = 'hidden';
            });

            closeMobileMenu.addEventListener('click', () => {
                mobileMenu.classList.remove('open');
                document.body.style.overflow = 'auto';
            });

            // Close menu when clicking outside
            mobileMenu.addEventListener('click', (e) => {
                if (e.target === mobileMenu) {
                    mobileMenu.classList.remove('open');
                    document.body.style.overflow = 'auto';
                }
            });
        }

        // Render properties with modern card design
        function renderProperties() {
            const grid = document.getElementById('propertiesGrid');
            const loadingState = document.getElementById('loadingState');
            const noResultsState = document.getElementById('noResultsState');

            if (filteredProperties.length === 0) {
                grid.innerHTML = '';
                loadingState.classList.add('hidden');
                noResultsState.classList.remove('hidden');
                return;
            }

            noResultsState.classList.add('hidden');
            loadingState.classList.add('hidden');

            grid.innerHTML = filteredProperties.map(property => `
                <article class="property-card bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
                         itemscope itemtype="https://schema.org/Accommodation">
                    <div class="relative">
                        ${createImageGallery(property)}
                        <button onclick="reportProperty('${property._id}')"
                                class="absolute top-4 right-4 bg-white bg-opacity-90 p-2 rounded-full hover:bg-opacity-100 transition-all shadow-lg z-10"
                                title="Report this property">
                            <i class="fas fa-flag text-red-500"></i>
                        </button>
                        ${property.featured ? '<div class="absolute top-4 left-4 bg-accent text-white px-3 py-1 rounded-full text-sm font-semibold z-10">Featured</div>' : ''}
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-2" itemprop="name">${property.title}</h3>
                        <p class="text-gray-600 mb-3 flex items-center" itemprop="address">
                            <i class="fas fa-map-marker-alt mr-2 text-primary"></i>${property.city}
                        </p>
                        <p class="text-3xl font-bold text-primary mb-4" itemprop="priceRange">₹${formatIndianCurrency(property.price)}<span class="text-lg text-gray-500">/month</span></p>

                        <!-- Action Buttons -->
                        <div class="space-y-3 mb-4">
                            <button onclick="openModal('${property._id}')"
                                    class="w-full bg-primary text-white py-3 px-4 rounded-xl hover:bg-purple-700 transition-colors font-semibold">
                                View Details
                            </button>
                            <button onclick="chatOnWhatsApp('${property.phone}', '${property.title}')"
                                    class="w-full bg-secondary text-white py-3 px-4 rounded-xl hover:bg-green-700 transition-colors font-semibold flex items-center justify-center">
                                <i class="fab fa-whatsapp mr-2 text-lg"></i>Chat on WhatsApp
                            </button>
                        </div>

                        <!-- Community Rating -->
                        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                            <span class="text-sm font-medium text-gray-700">Community Rating</span>
                            <div class="flex items-center space-x-4">
                                <button onclick="vote('${property._id}', 'up')"
                                        class="flex items-center space-x-1 text-secondary hover:text-green-700 transition-colors">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span id="upvotes-${property._id}" class="font-semibold">${property.upvotes || 0}</span>
                                </button>
                                <button onclick="vote('${property._id}', 'down')"
                                        class="flex items-center space-x-1 text-red-500 hover:text-red-700 transition-colors">
                                    <i class="fas fa-thumbs-down"></i>
                                    <span id="downvotes-${property._id}" class="font-semibold">${property.downvotes || 0}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </article>
            `).join('');
        }

        // Create modal image gallery
        function createModalImageGallery(property) {
            const images = property.image_urls && property.image_urls.length > 0
                ? property.image_urls
                : ['/placeholder.svg?height=300&width=400'];

            const modalGalleryId = `modal-gallery-${property._id}`;

            if (images.length === 1) {
                return `<img src="${images[0]}" alt="${property.title}" class="w-full h-64 object-cover rounded-xl">`;
            }

            return `
                <div class="image-gallery relative w-full h-64 overflow-hidden rounded-xl" id="${modalGalleryId}" data-current-index="0" data-total-images="${images.length}">
                    <div class="gallery-container flex transition-transform duration-300 ease-in-out h-full w-full">
                        ${images.map((url, index) => `
                            <img src="${url}"
                                 alt="${property.title} - Image ${index + 1}"
                                 class="w-full h-full object-cover flex-shrink-0"
                                 loading="lazy">
                        `).join('')}
                    </div>

                    <!-- Navigation arrows -->
                    <button onclick="previousModalImage('${property._id}')"
                            class="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-70 transition-all z-10"
                            aria-label="Previous image">
                        <i class="fas fa-chevron-left text-lg"></i>
                    </button>
                    <button onclick="nextModalImage('${property._id}')"
                            class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-70 transition-all z-10"
                            aria-label="Next image">
                        <i class="fas fa-chevron-right text-lg"></i>
                    </button>

                    <!-- Image indicators -->
                    <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                        ${images.map((_, index) => `
                            <button onclick="goToModalImage('${property._id}', ${index})"
                                    class="w-3 h-3 rounded-full transition-all indicator-dot ${index === 0 ? 'bg-white bg-opacity-100' : 'bg-white bg-opacity-50 hover:bg-opacity-75'}"
                                    data-index="${index}"
                                    aria-label="Go to image ${index + 1}"></button>
                        `).join('')}
                    </div>

                    <!-- Image counter -->
                    <div class="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm z-10">
                        <span class="current-image">1</span> / ${images.length}
                    </div>

                    <!-- Full screen button -->
                    <button onclick="openFullscreenGallery('${property._id}')"
                            class="absolute top-4 left-4 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all z-10"
                            aria-label="View fullscreen">
                        <i class="fas fa-expand text-sm"></i>
                    </button>
                </div>
            `;
        }

        // Open property details modal
        function openModal(propertyId) {
            const property = properties.find(p => p._id === propertyId);
            if (!property) return;

            const modal = document.getElementById('propertyModal');
            const modalContent = document.getElementById('modalContent');

            modalContent.innerHTML = `
                <div class="space-y-6">
                    ${createModalImageGallery(property)}

                    <div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">${property.title}</h3>
                        <p class="text-gray-600 mb-2 flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-primary"></i>${property.city}
                        </p>
                        <p class="text-3xl font-bold text-primary mb-4">₹${formatIndianCurrency(property.price)}<span class="text-lg text-gray-500">/month</span></p>
                    </div>

                    <div>
                        <h4 class="font-bold text-gray-900 mb-2">Description</h4>
                        <div class="scrollable-description text-gray-600 leading-relaxed">
                            ${property.description}
                        </div>
                    </div>

                    <div>
                        <h4 class="font-bold text-gray-900 mb-3">Bedrooms & Bathrooms</h4>
                        <p class="text-gray-600">${property.bedrooms} ${property.bedrooms === 1 ? 'Bedroom' : 'Bedrooms'}, ${property.bathrooms} ${property.bathrooms === 1 ? 'Bathroom' : 'Bathrooms'}</p>
                    </div>

                    <div>
                        <h4 class="font-bold text-gray-900 mb-3">Furnished Status</h4>
                        <p class="text-gray-600">${property.furnished_status === 'furnished' ? 'Fully Furnished' : property.furnished_status === 'semi' ? 'Semi Furnished' : 'Unfurnished'}</p>
                    </div>

                    <div>
                        <h4 class="font-bold text-gray-900 mb-3">Amenities</h4>
                        <div class="grid grid-cols-2 gap-2">
                            ${(property.amenities || []).map(amenity => `
                                <span class="bg-primary bg-opacity-10 text-primary text-sm px-3 py-2 rounded-lg font-medium">
                                    <i class="fas fa-check mr-2"></i>${amenity}
                                </span>
                            `).join('')}
                        </div>
                    </div>

                    <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                        <span class="font-semibold text-gray-700">Community Rating</span>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-1 text-secondary">
                                <i class="fas fa-thumbs-up"></i>
                                <span class="font-semibold">${property.upvotes || 0}</span>
                            </div>
                            <div class="flex items-center space-x-1 text-red-500">
                                <i class="fas fa-thumbs-down"></i>
                                <span class="font-semibold">${property.downvotes || 0}</span>
                            </div>
                        </div>
                    </div>

                    <button onclick="chatOnWhatsApp('${property.phone}', '${property.title}')"
                            class="w-full bg-secondary text-white py-4 px-6 rounded-xl hover:bg-green-700 transition-colors font-semibold text-lg flex items-center justify-center">
                        <i class="fab fa-whatsapp mr-3 text-xl"></i>Contact Owner
                    </button>
                </div>
            `;

            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // Close modal
        function closeModal() {
            const modal = document.getElementById('propertyModal');
            modal.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // Initialize search functionality
        function initializeSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchProperties();
                }
            });
        }

        // Enhanced search functionality with backend integration
        async function searchProperties() {
            const searchTerm = document.getElementById('searchInput').value.trim();
            const locationFilter = document.getElementById('locationFilter').value;
            const priceFilter = document.getElementById('priceFilter').value;

            try {
                document.getElementById('loadingState').classList.remove('hidden');
                document.getElementById('noResultsState').classList.add('hidden');

                // If no search term and no location filter, load all properties
                if (!searchTerm && !locationFilter) {
                    await loadPropertiesFromBackend();
                    return;
                }

                // Build query parameters
                const params = new URLSearchParams();
                if (searchTerm) params.append('search', searchTerm);
                if (locationFilter) {
                    // Combine search term with location filter
                    const combinedSearch = `${searchTerm} ${locationFilter}`.trim();
                    params.set('search', combinedSearch);
                }

                const response = await fetch(`/api/properties?${params.toString()}`);
                if (!response.ok) {
                    throw new Error('Search failed');
                }

                const data = await response.json();
                if (data.success) {
                    // Always update the main properties array with fresh data
                    properties = data.data || [];
                    filteredProperties = [...properties];

                    // Apply client-side sorting
                    applyFilters();

                    // Show appropriate state
                    if (properties.length === 0) {
                        document.getElementById('loadingState').classList.add('hidden');
                        document.getElementById('noResultsState').classList.remove('hidden');
                    } else {
                        document.getElementById('loadingState').classList.add('hidden');
                        document.getElementById('noResultsState').classList.add('hidden');
                    }
                } else {
                    throw new Error(data.error || 'Search failed');
                }

            } catch (error) {
                console.error('Search error:', error);
                document.getElementById('loadingState').classList.add('hidden');

                // If search fails, reload all properties to reset state
                if (!searchTerm && !locationFilter) {
                    await loadPropertiesFromBackend();
                } else {
                    // Show no results for failed search
                    properties = [];
                    filteredProperties = [];
                    renderProperties();
                    document.getElementById('noResultsState').classList.remove('hidden');
                }
            }
        }

        // Filter functionality
        function filterProperties() {
            applyFilters();
        }

        function applyFilters() {
            const priceFilter = document.getElementById('priceFilter').value;
            const locationFilter = document.getElementById('locationFilter').value;

            let filtered = [...filteredProperties];

            // Apply location filter
            if (locationFilter) {
                filtered = filtered.filter(property =>
                    property.location.toLowerCase().includes(locationFilter.toLowerCase())
                );
            }

            // Apply price sorting
            switch (priceFilter) {
                case 'low-to-high':
                    filtered.sort((a, b) => a.price - b.price);
                    break;
                case 'high-to-low':
                    filtered.sort((a, b) => b.price - a.price);
                    break;
                case 'recommended':
                    filtered.sort((a, b) => (b.upvotes - b.downvotes) - (a.upvotes - a.downvotes));
                    break;
            }

            filteredProperties = filtered;
            renderProperties();
        }

        // Voting functionality
        function vote(propertyId, type) {
            const property = properties.find(p => p._id === propertyId);
            if (property) {
                // Send vote to backend first
                sendVoteToBackend(propertyId, type);
            }
        }

        // WhatsApp functionality with property context
        function chatOnWhatsApp(phone, propertyTitle) {
            const message = encodeURIComponent(`Hi! I'm interested in "${propertyTitle}" that I found on HavenHuts. Could you please provide more details?`);
            window.open(`https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=${message}`, '_blank');
        }

        // Report functionality
        function reportProperty(propertyId) {
            if (confirm('Are you sure you want to report this property? This will help us maintain quality listings.')) {
                sendReportToBackend(propertyId);
                alert('Property reported successfully. Thank you for helping us maintain quality listings.');
            }
        }

        // Backend integration functions
        async function sendVoteToBackend(propertyId, voteType) {
            try {
                const response = await fetch('/api/vote', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        property_id: propertyId,
                        vote_type: voteType,
                        timestamp: new Date().toISOString()
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to submit vote');
                }

                const result = await response.json();
                if (result.success) {
                    // Update the UI with new vote counts
                    document.getElementById(`upvotes-${propertyId}`).textContent = result.upvotes;
                    document.getElementById(`downvotes-${propertyId}`).textContent = result.downvotes;

                    // Update the property in our local array
                    const property = properties.find(p => p._id === propertyId);
                    if (property) {
                        property.upvotes = result.upvotes;
                        property.downvotes = result.downvotes;
                    }
                }
            } catch (error) {
                console.error('Error submitting vote:', error);

                // Show specific error message if available
                if (error.message.includes('already voted')) {
                    alert('You have already voted on this property recently. Please wait 24 hours before voting again.');
                } else if (error.message.includes('rate limit')) {
                    alert('Too many votes. Please wait a moment before voting again.');
                } else {
                    alert('Failed to submit vote. Please try again later.');
                }
            }
        }

        async function sendReportToBackend(propertyId) {
            try {
                const response = await fetch('/api/report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        property_id: propertyId,
                        reason: 'User reported content',
                        timestamp: new Date().toISOString()
                    })
                });

                if (!response.ok) {
                    throw new Error('Failed to submit report');
                }
            } catch (error) {
                console.error('Error submitting report:', error);
            }
        }

        // Load properties from backend with SEO data
        async function loadPropertiesFromBackend() {
            try {
                document.getElementById('loadingState').classList.remove('hidden');

                const response = await fetch('/api/properties');
                if (!response.ok) {
                    throw new Error('Failed to fetch properties');
                }

                const data = await response.json();
                if (data.success) {
                    properties = data.data || [];
                    filteredProperties = [...properties];
                    renderProperties();
                } else {
                    throw new Error(data.error || 'Failed to fetch properties');
                }
            } catch (error) {
                console.error('Error loading properties:', error);
                document.getElementById('loadingState').classList.add('hidden');
                document.getElementById('noResultsState').classList.remove('hidden');
            }
        }

        // Modal gallery navigation functions
        function nextModalImage(propertyId) {
            const gallery = document.getElementById(`modal-gallery-${propertyId}`);
            if (!gallery) return;

            const container = gallery.querySelector('.gallery-container');
            const images = container.querySelectorAll('img');
            const indicators = gallery.querySelectorAll('.indicator-dot');
            const counter = gallery.querySelector('.current-image');

            let currentIndex = parseInt(gallery.dataset.currentIndex || '0');
            currentIndex = (currentIndex + 1) % images.length;

            updateGallery(container, indicators, counter, currentIndex, images.length);
        }

        function previousModalImage(propertyId) {
            const gallery = document.getElementById(`modal-gallery-${propertyId}`);
            if (!gallery) return;

            const container = gallery.querySelector('.gallery-container');
            const images = container.querySelectorAll('img');
            const indicators = gallery.querySelectorAll('.indicator-dot');
            const counter = gallery.querySelector('.current-image');

            let currentIndex = parseInt(gallery.dataset.currentIndex || '0');
            currentIndex = currentIndex <= 0 ? images.length - 1 : currentIndex - 1;

            updateGallery(container, indicators, counter, currentIndex, images.length);
        }

        function goToModalImage(propertyId, index) {
            const gallery = document.getElementById(`modal-gallery-${propertyId}`);
            if (!gallery) return;

            const container = gallery.querySelector('.gallery-container');
            const images = container.querySelectorAll('img');
            const indicators = gallery.querySelectorAll('.indicator-dot');
            const counter = gallery.querySelector('.current-image');

            updateGallery(container, indicators, counter, index, images.length);
        }

        // Fullscreen gallery functionality
        function openFullscreenGallery(propertyId) {
            const property = properties.find(p => p._id === propertyId);
            if (!property || !property.image_urls || property.image_urls.length === 0) return;

            const currentGallery = document.getElementById(`modal-gallery-${propertyId}`);
            const currentIndex = currentGallery ? parseInt(currentGallery.dataset.currentIndex || '0') : 0;

            // Create fullscreen modal
            const fullscreenModal = document.createElement('div');
            fullscreenModal.className = 'fixed inset-0 bg-black z-50 flex items-center justify-center';
            fullscreenModal.id = `fullscreen-${propertyId}`;

            fullscreenModal.innerHTML = `
                <div class="relative w-full h-full flex items-center justify-center">
                    <div class="image-gallery relative w-full h-full" data-current-index="${currentIndex}">
                        <div class="gallery-container flex transition-transform duration-300 ease-in-out h-full w-full">
                            ${property.image_urls.map((url, index) => `
                                <img src="${url}"
                                     alt="${property.title} - Image ${index + 1}"
                                     class="w-full h-full object-contain flex-shrink-0"
                                     loading="lazy">
                            `).join('')}
                        </div>

                        <!-- Navigation arrows -->
                        <button onclick="previousFullscreenImage('${propertyId}')"
                                class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-4 rounded-full hover:bg-opacity-70 transition-all z-10">
                            <i class="fas fa-chevron-left text-xl"></i>
                        </button>
                        <button onclick="nextFullscreenImage('${propertyId}')"
                                class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-4 rounded-full hover:bg-opacity-70 transition-all z-10">
                            <i class="fas fa-chevron-right text-xl"></i>
                        </button>

                        <!-- Close button -->
                        <button onclick="closeFullscreenGallery('${propertyId}')"
                                class="absolute top-4 right-4 bg-black bg-opacity-50 text-white p-3 rounded-full hover:bg-opacity-70 transition-all z-10">
                            <i class="fas fa-times text-xl"></i>
                        </button>

                        <!-- Image counter -->
                        <div class="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-4 py-2 rounded-full text-lg z-10">
                            <span class="current-image">${currentIndex + 1}</span> / ${property.image_urls.length}
                        </div>

                        <!-- Image indicators -->
                        <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                            ${property.image_urls.map((_, index) => `
                                <button onclick="goToFullscreenImage('${propertyId}', ${index})"
                                        class="w-3 h-3 rounded-full transition-all indicator-dot ${index === currentIndex ? 'bg-white bg-opacity-100' : 'bg-white bg-opacity-50 hover:bg-opacity-75'}"
                                        data-index="${index}"></button>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(fullscreenModal);
            document.body.style.overflow = 'hidden';

            // Set initial position
            const container = fullscreenModal.querySelector('.gallery-container');
            container.style.transform = `translateX(-${currentIndex * 100}%)`;
        }

        function closeFullscreenGallery(propertyId) {
            const fullscreenModal = document.getElementById(`fullscreen-${propertyId}`);
            if (fullscreenModal) {
                document.body.removeChild(fullscreenModal);
                document.body.style.overflow = 'auto';
            }
        }

        function nextFullscreenImage(propertyId) {
            const gallery = document.querySelector(`#fullscreen-${propertyId} .image-gallery`);
            if (!gallery) return;

            const container = gallery.querySelector('.gallery-container');
            const images = container.querySelectorAll('img');
            const indicators = gallery.querySelectorAll('.indicator-dot');
            const counter = gallery.querySelector('.current-image');

            let currentIndex = parseInt(gallery.dataset.currentIndex || '0');
            currentIndex = (currentIndex + 1) % images.length;

            updateGallery(container, indicators, counter, currentIndex, images.length);
        }

        function previousFullscreenImage(propertyId) {
            const gallery = document.querySelector(`#fullscreen-${propertyId} .image-gallery`);
            if (!gallery) return;

            const container = gallery.querySelector('.gallery-container');
            const images = container.querySelectorAll('img');
            const indicators = gallery.querySelectorAll('.indicator-dot');
            const counter = gallery.querySelector('.current-image');

            let currentIndex = parseInt(gallery.dataset.currentIndex || '0');
            currentIndex = currentIndex <= 0 ? images.length - 1 : currentIndex - 1;

            updateGallery(container, indicators, counter, currentIndex, images.length);
        }

        function goToFullscreenImage(propertyId, index) {
            const gallery = document.querySelector(`#fullscreen-${propertyId} .image-gallery`);
            if (!gallery) return;

            const container = gallery.querySelector('.gallery-container');
            const images = container.querySelectorAll('img');
            const indicators = gallery.querySelectorAll('.indicator-dot');
            const counter = gallery.querySelector('.current-image');

            updateGallery(container, indicators, counter, index, images.length);
        }

        // Close modal when clicking outside
        document.getElementById('propertyModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "HavenHuts",
        "description": "Find your perfect room rental with HavenHuts - premium accommodations and verified properties",
        "url": "https://havenhuts.com",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://havenhuts.com/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>

    <!-- Kolkata Love Footer -->
    <footer class="bg-gradient-to-r from-purple-900 to-indigo-900 text-white py-12 mt-16">
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-3 gap-8">
                <!-- About Section -->
                <div>
                    <h3 class="text-2xl font-bold mb-4 text-yellow-300">HavenHuts</h3>
                    <p class="text-gray-300 mb-4">
                        Your trusted partner for finding the perfect room rental in your preferred area.
                        We understand the unique charm and needs of the people.
                    </p>
                    <div class="flex items-center text-yellow-300">
                        <i class="fas fa-heart mr-2 text-red-400 animate-pulse"></i>
                        <span class="font-semibold">Made with love in Kolkata</span>
                    </div>
                </div>

                <!-- Popular Areas -->
                <div>
                    <h4 class="text-xl font-bold mb-4 text-yellow-300">Popular Areas</h4>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <a href="#" onclick="searchByArea('salt lake')" class="text-gray-300 hover:text-yellow-300 transition-colors">Salt Lake</a>
                        <a href="#" onclick="searchByArea('park street')" class="text-gray-300 hover:text-yellow-300 transition-colors">Park Street</a>
                        <a href="#" onclick="searchByArea('ballygunge')" class="text-gray-300 hover:text-yellow-300 transition-colors">Ballygunge</a>
                        <a href="#" onclick="searchByArea('new town')" class="text-gray-300 hover:text-yellow-300 transition-colors">New Town</a>
                        <a href="#" onclick="searchByArea('jadavpur')" class="text-gray-300 hover:text-yellow-300 transition-colors">Jadavpur</a>
                        <a href="#" onclick="searchByArea('howrah')" class="text-gray-300 hover:text-yellow-300 transition-colors">Howrah</a>
                        <a href="#" onclick="searchByArea('tollygunge')" class="text-gray-300 hover:text-yellow-300 transition-colors">Tollygunge</a>
                        <a href="#" onclick="searchByArea('gariahat')" class="text-gray-300 hover:text-yellow-300 transition-colors">Gariahat</a>
                    </div>
                </div>

                <!-- Contact & Social -->
                <div>
                    <h4 class="text-xl font-bold mb-4 text-yellow-300">Connect With Us</h4>
                    <div class="space-y-3">
                        <div class="flex items-center text-gray-300">
                            <i class="fas fa-map-marker-alt mr-3 text-yellow-300"></i>
                            <span>Serving Greater Kolkata Area</span>
                        </div>
                        <div class="flex items-center text-gray-300">
                            <i class="fas fa-envelope mr-3 text-yellow-300"></i>
                            <span><EMAIL></span>
                        </div>
                        <div class="flex space-x-4 mt-4">
                            <a href="#" class="text-gray-300 hover:text-yellow-300 transition-colors">
                                <i class="fab fa-facebook text-xl"></i>
                            </a>
                            <a href="#" class="text-gray-300 hover:text-yellow-300 transition-colors">
                                <i class="fab fa-twitter text-xl"></i>
                            </a>
                            <a href="#" class="text-gray-300 hover:text-yellow-300 transition-colors">
                                <i class="fab fa-instagram text-xl"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Bar -->
            <div class="border-t border-purple-700 mt-8 pt-6 text-center">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-300 text-sm">
                        © 2024 HavenHuts. All rights reserved.
                    </p>
                    <div class="flex items-center mt-2 md:mt-0 text-sm text-gray-300">
                        <span>Built with</span>
                        <i class="fas fa-heart mx-2 text-red-400 animate-pulse"></i>
                        <span>in Kolkata, for India</span>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Function to search by area from footer links
        function searchByArea(area) {
            document.getElementById('searchInput').value = area;
            searchProperties();
        }

        // Function to load all properties from backend
        async function loadPropertiesFromBackend() {
            try {
                document.getElementById('loadingState').classList.remove('hidden');
                document.getElementById('noResultsState').classList.add('hidden');

                const response = await fetch('/api/properties');
                if (!response.ok) {
                    throw new Error('Failed to load properties');
                }

                const data = await response.json();
                if (data.success) {
                    properties = data.data || [];
                    filteredProperties = [...properties];
                    applyFilters();
                } else {
                    throw new Error(data.error || 'Failed to load properties');
                }

                document.getElementById('loadingState').classList.add('hidden');

            } catch (error) {
                console.error('Error loading properties:', error);
                document.getElementById('loadingState').classList.add('hidden');
                document.getElementById('noResultsState').classList.remove('hidden');
            }
        }
    </script>
</body>
</html>