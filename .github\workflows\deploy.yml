name: Deploy HavenHuts to CapRover

on:
  push:
    branches: [main]
  workflow_dispatch:  # Allow manual deployment

jobs:
  deploy:
    runs-on: ubuntu-latest

    env:
      NODE_TLS_REJECT_UNAUTHORIZED: '0'

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install Dependencies
        run: |
          pip install --upgrade pip
          pip install -r requirements.txt

      - name: Run Tests (Optional)
        run: |
          # Add your test commands here if you have tests
          python -c "import app; print(' App imports successfully')"
          echo " Basic validation passed"

      # Create deployment bundle (Robust method)
      - name: Create deployment bundle
        run: |
          echo "📦 Creating deployment bundle..."

          # Ensure static directory exists with content
          mkdir -p static/css static/js
          echo "/* CSS files go here */" > static/css/placeholder.css
          echo "// JS files go here" > static/js/placeholder.js

          # List all files before bundling
          echo "📋 Current directory contents:"
          ls -la

          echo "📋 Templates directory:"
          ls -la templates/ || echo "Templates directory not found"

          echo "📋 Static directory:"
          ls -la static/ || echo "Static directory not found"

          # Create tar file with explicit file list
          echo "📦 Creating deployment archive..."
          tar -czf deploy.tar \
            app.py \
            wsgi.py \
            requirements.txt \
            Dockerfile \
            captain-definition \
            .env.example \
            sitemap.xml \
            templates \
            static

          echo "✅ Deployment bundle created successfully!"
          echo "� Bundle size: $(du -h deploy.tar | cut -f1)"
          echo "📋 Bundle contents:"
          tar -tzf deploy.tar | head -20

      # Deploy to CapRover
      - name: Deploy to CapRover
        uses: caprover/deploy-from-github@v1.1.2
        with:
          server: ${{ secrets.CAPROVER_SERVER }}
          app: ${{ secrets.APP_NAME }}
          token: ${{ secrets.APP_TOKEN }}

      - name: Deployment Success Notification
        if: success()
        run: |
          echo " HavenHuts deployed successfully to CapRover!"
          echo " Your app should be available at: https://${{ secrets.APP_NAME }}.${{ secrets.CAPROVER_SERVER }}"

      - name: Deployment Failure Notification
        if: failure()
        run: |
          echo " Deployment failed. Please check the logs above."
          echo "🔧 Common issues:"
          echo "   • Check CapRover server URL and credentials"
          echo "   • Verify app name exists in CapRover"
          echo "   • Ensure all required secrets are set"
