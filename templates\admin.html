<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - HavenHuts</title>
    <!-- Fallback CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">

    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome with fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          onerror="this.onerror=null;this.href='https://use.fontawesome.com/releases/v6.4.0/css/all.css';">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#4F46E5',
                        secondary: '#10B981',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold text-primary">
                        <i class="fas fa-shield-alt mr-2"></i>HavenHuts Admin
                    </h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="text-gray-700 hover:text-primary transition-colors">
                        <i class="fas fa-home mr-1"></i>Back to Site
                    </a>
                    <button onclick="logout()" class="text-red-600 hover:text-red-700 transition-colors">
                        <i class="fas fa-sign-out-alt mr-1"></i>Logout
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Pending</p>
                        <p id="pendingCount" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 text-red-600">
                        <i class="fas fa-flag text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Reported</p>
                        <p id="reportedCount" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-check text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Approved</p>
                        <p id="approvedCount" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-home text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total</p>
                        <p id="totalCount" class="text-2xl font-bold text-gray-900">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Tabs with Filters -->
        <div class="bg-white rounded-lg shadow">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6">
                    <button onclick="showTab('all')" id="allTab"
                            class="tab-button py-4 px-1 border-b-2 font-medium text-sm border-primary text-primary">
                        All Properties
                    </button>
                    <button onclick="showTab('pending')" id="pendingTab"
                            class="tab-button py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        Pending Properties
                    </button>
                    <button onclick="showTab('reported')" id="reportedTab"
                            class="tab-button py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        Reported Properties
                    </button>
                </nav>
            </div>

            <!-- Filters and Search Bar -->
            <div class="p-6 border-b border-gray-200 bg-gray-50">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Search Properties</label>
                        <input type="text" id="adminSearchInput" placeholder="Search by title, city, or description..."
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select id="statusFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                            <option value="">All Statuses</option>
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                        </select>
                    </div>

                    <!-- Date Range -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                        <select id="dateFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                            <option value="">All Time</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                        </select>
                    </div>

                    <!-- Sort By -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                        <select id="sortFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                            <option value="created_at_desc">Newest First</option>
                            <option value="created_at_asc">Oldest First</option>
                            <option value="price_desc">Highest Price</option>
                            <option value="price_asc">Lowest Price</option>
                            <option value="votes_desc">Most Voted</option>
                        </select>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-between items-center mt-4">
                    <div class="flex space-x-2">
                        <button onclick="applyFilters()" class="bg-primary text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors">
                            <i class="fas fa-search mr-2"></i>Apply Filters
                        </button>
                        <button onclick="clearFilters()" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors">
                            <i class="fas fa-times mr-2"></i>Clear
                        </button>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="flex space-x-2" id="bulkActions" style="display: none;">
                        <button onclick="bulkApprove()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                            <i class="fas fa-check mr-2"></i>Bulk Approve
                        </button>
                        <button onclick="bulkReject()" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors">
                            <i class="fas fa-times mr-2"></i>Bulk Reject
                        </button>
                        <button onclick="bulkDelete()" class="bg-gray-800 text-white px-4 py-2 rounded-md hover:bg-gray-900 transition-colors">
                            <i class="fas fa-trash mr-2"></i>Bulk Delete
                        </button>
                    </div>
                </div>
            </div>

            <!-- All Properties Tab -->
            <div id="allContent" class="tab-content p-6">
                <div class="mb-4 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">All Properties</h3>
                    <div class="flex items-center space-x-2">
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="rounded border-gray-300 text-primary focus:ring-primary">
                        <label for="selectAll" class="text-sm text-gray-700">Select All</label>
                    </div>
                </div>
                <div id="allProperties" class="space-y-4">
                    <!-- Properties will be loaded here -->
                </div>
            </div>

            <!-- Pending Properties Tab -->
            <div id="pendingContent" class="tab-content p-6 hidden">
                <div id="pendingProperties" class="space-y-4">
                    <!-- Properties will be loaded here -->
                </div>
            </div>

            <!-- Reported Properties Tab -->
            <div id="reportedContent" class="tab-content p-6 hidden">
                <div id="reportedProperties" class="space-y-4">
                    <!-- Properties will be loaded here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Property Detail Modal -->
    <div id="propertyModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">Property Details</h2>
                        <button onclick="closePropertyModal()" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div id="propertyModalContent">
                        <!-- Property details will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Property Modal -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">Edit Property</h2>
                        <button onclick="closeEditModal()" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div id="editModalContent">
                        <!-- Edit form will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentTab = 'all';
        let allPropertiesData = [];
        let selectedProperties = new Set();

        // Format currency in Indian style (₹1,50,000)
        function formatIndianCurrency(amount) {
            // Convert to string and handle decimal places
            const numStr = Math.round(amount).toString();

            // Indian numbering system: last 3 digits, then groups of 2
            if (numStr.length <= 3) {
                return numStr;
            }

            const lastThree = numStr.substring(numStr.length - 3);
            const otherNumbers = numStr.substring(0, numStr.length - 3);

            if (otherNumbers !== '') {
                return otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ',') + ',' + lastThree;
            } else {
                return lastThree;
            }
        }

        // Initialize admin panel
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardStats();
            loadAllProperties();

            // Add event listeners for filters
            document.getElementById('adminSearchInput').addEventListener('input', debounce(applyFilters, 300));
            document.getElementById('statusFilter').addEventListener('change', applyFilters);
            document.getElementById('dateFilter').addEventListener('change', applyFilters);
            document.getElementById('sortFilter').addEventListener('change', applyFilters);
        });

        // Debounce function for search input
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Tab switching
        function showTab(tab) {
            currentTab = tab;

            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.className = 'tab-button py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300';
            });
            document.getElementById(tab + 'Tab').className = 'tab-button py-4 px-1 border-b-2 font-medium text-sm border-primary text-primary';

            // Update content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            document.getElementById(tab + 'Content').classList.remove('hidden');

            // Load appropriate data
            if (tab === 'all') {
                loadAllProperties();
            } else if (tab === 'pending') {
                loadPendingProperties();
            } else if (tab === 'reported') {
                loadReportedProperties();
            }
        }

        // Load dashboard statistics
        async function loadDashboardStats() {
            try {
                const response = await fetch('/admin', {
                    headers: {
                        'Authorization': 'Basic ' + btoa('admin:admin123') // Replace with actual credentials
                    }
                });

                if (!response.ok) throw new Error('Failed to load stats');

                const data = await response.json();
                if (data.success) {
                    document.getElementById('pendingCount').textContent = data.stats.pending;
                    document.getElementById('reportedCount').textContent = data.stats.reported;
                    document.getElementById('approvedCount').textContent = data.stats.approved;
                    document.getElementById('totalCount').textContent = data.stats.total;
                }
            } catch (error) {
                console.error('Error loading dashboard stats:', error);
            }
        }

        // Load pending properties
        async function loadPendingProperties() {
            try {
                const response = await fetch('/admin/properties/pending', {
                    headers: {
                        'Authorization': 'Basic ' + btoa('admin:admin123') // Replace with actual credentials
                    }
                });

                if (!response.ok) throw new Error('Failed to load pending properties');

                const data = await response.json();
                if (data.success) {
                    renderPendingProperties(data.data);
                }
            } catch (error) {
                console.error('Error loading pending properties:', error);
            }
        }

        // Load reported properties
        async function loadReportedProperties() {
            try {
                const response = await fetch('/admin/properties/reported', {
                    headers: {
                        'Authorization': 'Basic ' + btoa('admin:admin123') // Replace with actual credentials
                    }
                });

                if (!response.ok) throw new Error('Failed to load reported properties');

                const data = await response.json();
                if (data.success) {
                    renderReportedProperties(data.data);
                }
            } catch (error) {
                console.error('Error loading reported properties:', error);
            }
        }

        // Load all properties with enhanced functionality
        async function loadAllProperties() {
            try {
                const response = await fetch('/api/properties?admin=true', {
                    credentials: 'include'
                });

                if (!response.ok) throw new Error('Failed to load all properties');

                const data = await response.json();
                if (data.success) {
                    allPropertiesData = data.data || [];
                    applyFilters();
                }
            } catch (error) {
                console.error('Error loading all properties:', error);
            }
        }

        // Enhanced property rendering with action menu
        function renderAllProperties(properties) {
            const container = document.getElementById('allProperties');

            if (properties.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">No properties found</p>';
                return;
            }

            container.innerHTML = properties.map(property => `
                <div class="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div class="flex items-start space-x-4">
                        <!-- Checkbox for bulk selection -->
                        <input type="checkbox" class="property-checkbox mt-2" value="${property._id}"
                               onchange="updateBulkActions()" />

                        <!-- Property thumbnail -->
                        <div class="flex-shrink-0">
                            <img src="${property.image_urls && property.image_urls[0] ? property.image_urls[0] : '/placeholder.svg?height=80&width=80'}"
                                 alt="${property.title}" class="w-20 h-20 object-cover rounded-lg">
                        </div>

                        <!-- Property details -->
                        <div class="flex-1 min-w-0">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900 truncate">${property.title}</h3>
                                    <p class="text-gray-600 flex items-center mt-1">
                                        <i class="fas fa-map-marker-alt mr-1 text-primary"></i>
                                        ${property.city}${property.area ? `, ${property.area}` : ''}
                                    </p>
                                    <p class="text-xl font-bold text-primary mt-1">₹${formatIndianCurrency(property.price)}/month</p>
                                    <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                                        <span><i class="fas fa-bed mr-1"></i>${property.bedrooms} bed</span>
                                        <span><i class="fas fa-bath mr-1"></i>${property.bathrooms} bath</span>
                                        <span><i class="fas fa-calendar mr-1"></i>${new Date(property.created_at).toLocaleDateString()}</span>
                                    </div>
                                </div>

                                <!-- Status badge -->
                                <div class="flex flex-col items-end space-y-2">
                                    <span class="px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(property.status)}">
                                        ${property.status.charAt(0).toUpperCase() + property.status.slice(1)}
                                    </span>
                                    ${property.reports && property.reports.length > 0 ?
                                        `<span class="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                                            <i class="fas fa-flag mr-1"></i>${property.reports.length} report(s)
                                        </span>` : ''}
                                    <div class="flex items-center space-x-1 text-sm">
                                        <span class="text-green-600"><i class="fas fa-thumbs-up"></i> ${property.upvotes || 0}</span>
                                        <span class="text-red-600"><i class="fas fa-thumbs-down"></i> ${property.downvotes || 0}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action menu -->
                        <div class="flex-shrink-0">
                            <div class="relative">
                                <button onclick="toggleActionMenu('${property._id}')"
                                        class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <div id="actionMenu-${property._id}" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                                    <div class="py-1">
                                        <button onclick="viewPropertyDetails('${property._id}')"
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-eye mr-2"></i>View Details
                                        </button>
                                        <button onclick="editProperty('${property._id}')"
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-edit mr-2"></i>Edit Property
                                        </button>
                                        <button onclick="inspectProperty('${property._id}')"
                                                class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            <i class="fas fa-search mr-2"></i>Inspect
                                        </button>
                                        <hr class="my-1">
                                        ${property.status === 'pending' ? `
                                            <button onclick="approveProperty('${property._id}')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-green-700 hover:bg-green-50">
                                                <i class="fas fa-check mr-2"></i>Approve
                                            </button>
                                            <button onclick="rejectProperty('${property._id}')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                                                <i class="fas fa-times mr-2"></i>Reject
                                            </button>
                                        ` : ''}
                                        ${property.reports && property.reports.length > 0 ? `
                                            <button onclick="dismissReports('${property._id}')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-blue-700 hover:bg-blue-50">
                                                <i class="fas fa-flag mr-2"></i>Dismiss Reports
                                            </button>
                                        ` : ''}
                                        <button onclick="deleteProperty('${property._id}')"
                                                class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50">
                                            <i class="fas fa-trash mr-2"></i>Delete
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Render pending properties
        function renderPendingProperties(properties) {
            const container = document.getElementById('pendingProperties');

            if (properties.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">No pending properties</p>';
                return;
            }

            container.innerHTML = properties.map(property => `
                <div class="border rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">${property.title}</h3>
                            <p class="text-gray-600">${property.city} - ₹${formatIndianCurrency(property.price)}/month</p>
                            <p class="text-sm text-gray-500 mt-1">${property.bedrooms} bed, ${property.bathrooms} bath</p>
                            <p class="text-sm text-gray-500">Created: ${new Date(property.created_at).toLocaleDateString()}</p>
                        </div>
                        <div class="flex space-x-2 ml-4">
                            <button onclick="viewPropertyDetails('${property._id}')"
                                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                <i class="fas fa-eye mr-1"></i>View
                            </button>
                            <button onclick="approveProperty('${property._id}')"
                                    class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                                <i class="fas fa-check mr-1"></i>Approve
                            </button>
                            <button onclick="rejectProperty('${property._id}')"
                                    class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                                <i class="fas fa-times mr-1"></i>Reject
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Render reported properties
        function renderReportedProperties(properties) {
            const container = document.getElementById('reportedProperties');

            if (properties.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">No reported properties</p>';
                return;
            }

            container.innerHTML = properties.map(property => `
                <div class="border rounded-lg p-4">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold">${property.title}</h3>
                            <p class="text-gray-600">${property.city} - ₹${formatIndianCurrency(property.price)}/month</p>
                            <p class="text-sm text-red-600 font-medium">${property.report_count} report(s)</p>
                        </div>
                        <div class="flex space-x-2 ml-4">
                            <button onclick="dismissReports('${property._id}')"
                                    class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                                <i class="fas fa-check mr-1"></i>Dismiss
                            </button>
                            <button onclick="deleteProperty('${property._id}')"
                                    class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                                <i class="fas fa-trash mr-1"></i>Delete
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Admin actions
        async function approveProperty(propertyId, reload = true) {
            if (!confirm('Approve this property?')) return;

            try {
                const response = await fetch(`/admin/properties/${propertyId}/approve`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'  // Include session cookies
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    if (reload) {
                        loadDashboardStats();
                        loadPendingProperties();
                        if (currentTab === 'all') loadAllProperties();
                        alert('Property approved successfully');
                    }
                    return true;
                } else {
                    throw new Error(result.error || 'Failed to approve property');
                }
            } catch (error) {
                console.error('Error approving property:', error);
                if (reload) alert('Failed to approve property: ' + error.message);
                return false;
            }
        }

        async function rejectProperty(propertyId, reload = true) {
            if (!confirm('Reject this property?')) return;

            try {
                const response = await fetch(`/admin/properties/${propertyId}/reject`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'  // Include session cookies
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    if (reload) {
                        loadDashboardStats();
                        loadPendingProperties();
                        if (currentTab === 'all') loadAllProperties();
                        alert('Property rejected successfully');
                    }
                    return true;
                } else {
                    throw new Error(result.error || 'Failed to reject property');
                }
            } catch (error) {
                console.error('Error rejecting property:', error);
                if (reload) alert('Failed to reject property: ' + error.message);
                return false;
            }
        }

        async function dismissReports(propertyId) {
            if (!confirm('Dismiss all reports for this property?')) return;

            try {
                const response = await fetch(`/admin/properties/${propertyId}/dismiss-reports`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    loadDashboardStats();
                    loadAllProperties();
                    alert('Reports dismissed successfully');
                } else {
                    throw new Error(result.error || 'Failed to dismiss reports');
                }
            } catch (error) {
                console.error('Error dismissing reports:', error);
                alert('Failed to dismiss reports: ' + error.message);
            }
        }

        async function deleteProperty(propertyId, reload = true) {
            if (!confirm('Delete this property permanently? This action cannot be undone.')) return;

            try {
                const response = await fetch(`/admin/properties/${propertyId}/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include'
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    if (reload) {
                        loadDashboardStats();
                        loadAllProperties();
                        alert('Property deleted successfully');
                    }
                    return true;
                } else {
                    throw new Error(result.error || 'Failed to delete property');
                }
            } catch (error) {
                console.error('Error deleting property:', error);
                if (reload) alert('Failed to delete property: ' + error.message);
                return false;
            }
        }

        // Helper functions
        function getStatusBadgeClass(status) {
            switch (status) {
                case 'pending': return 'bg-yellow-100 text-yellow-800';
                case 'approved': return 'bg-green-100 text-green-800';
                case 'rejected': return 'bg-red-100 text-red-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function toggleActionMenu(propertyId) {
            // Close all other menus
            document.querySelectorAll('[id^="actionMenu-"]').forEach(menu => {
                if (menu.id !== `actionMenu-${propertyId}`) {
                    menu.classList.add('hidden');
                }
            });

            // Toggle current menu
            const menu = document.getElementById(`actionMenu-${propertyId}`);
            menu.classList.toggle('hidden');
        }

        // Close action menus when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('[onclick*="toggleActionMenu"]')) {
                document.querySelectorAll('[id^="actionMenu-"]').forEach(menu => {
                    menu.classList.add('hidden');
                });
            }
        });

        // Filter and search functionality
        function applyFilters() {
            const searchTerm = document.getElementById('adminSearchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const dateFilter = document.getElementById('dateFilter').value;
            const sortFilter = document.getElementById('sortFilter').value;

            let filtered = [...allPropertiesData];

            // Apply search filter
            if (searchTerm) {
                filtered = filtered.filter(property =>
                    property.title.toLowerCase().includes(searchTerm) ||
                    property.city.toLowerCase().includes(searchTerm) ||
                    (property.area && property.area.toLowerCase().includes(searchTerm)) ||
                    (property.description && property.description.toLowerCase().includes(searchTerm))
                );
            }

            // Apply status filter
            if (statusFilter) {
                filtered = filtered.filter(property => property.status === statusFilter);
            }

            // Apply date filter
            if (dateFilter) {
                const now = new Date();
                const filterDate = new Date();

                switch (dateFilter) {
                    case 'today':
                        filterDate.setHours(0, 0, 0, 0);
                        break;
                    case 'week':
                        filterDate.setDate(now.getDate() - 7);
                        break;
                    case 'month':
                        filterDate.setMonth(now.getMonth() - 1);
                        break;
                }

                if (dateFilter !== '') {
                    filtered = filtered.filter(property =>
                        new Date(property.created_at) >= filterDate
                    );
                }
            }

            // Apply sorting
            switch (sortFilter) {
                case 'created_at_desc':
                    filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                    break;
                case 'created_at_asc':
                    filtered.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
                    break;
                case 'price_desc':
                    filtered.sort((a, b) => b.price - a.price);
                    break;
                case 'price_asc':
                    filtered.sort((a, b) => a.price - b.price);
                    break;
                case 'votes_desc':
                    filtered.sort((a, b) => ((b.upvotes || 0) - (b.downvotes || 0)) - ((a.upvotes || 0) - (a.downvotes || 0)));
                    break;
            }

            renderAllProperties(filtered);
        }

        function clearFilters() {
            document.getElementById('adminSearchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('dateFilter').value = '';
            document.getElementById('sortFilter').value = 'created_at_desc';
            applyFilters();
        }

        // Bulk actions functionality
        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.property-checkbox:checked');
            const bulkActions = document.getElementById('bulkActions');

            if (checkboxes.length > 0) {
                bulkActions.style.display = 'flex';
                selectedProperties.clear();
                checkboxes.forEach(cb => selectedProperties.add(cb.value));
            } else {
                bulkActions.style.display = 'none';
                selectedProperties.clear();
            }
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('.property-checkbox');

            checkboxes.forEach(cb => {
                cb.checked = selectAll.checked;
            });

            updateBulkActions();
        }

        async function bulkApprove() {
            if (selectedProperties.size === 0) return;

            if (!confirm(`Approve ${selectedProperties.size} selected properties?`)) return;

            try {
                for (const propertyId of selectedProperties) {
                    await approveProperty(propertyId, false); // false = don't reload individually
                }
                loadDashboardStats();
                loadAllProperties();
                alert(`${selectedProperties.size} properties approved successfully`);
            } catch (error) {
                console.error('Bulk approve error:', error);
                alert('Some properties failed to approve');
            }
        }

        async function bulkReject() {
            if (selectedProperties.size === 0) return;

            if (!confirm(`Reject ${selectedProperties.size} selected properties?`)) return;

            try {
                for (const propertyId of selectedProperties) {
                    await rejectProperty(propertyId, false); // false = don't reload individually
                }
                loadDashboardStats();
                loadAllProperties();
                alert(`${selectedProperties.size} properties rejected successfully`);
            } catch (error) {
                console.error('Bulk reject error:', error);
                alert('Some properties failed to reject');
            }
        }

        async function bulkDelete() {
            if (selectedProperties.size === 0) return;

            if (!confirm(`DELETE ${selectedProperties.size} selected properties? This action cannot be undone!`)) return;

            try {
                for (const propertyId of selectedProperties) {
                    await deleteProperty(propertyId, false); // false = don't reload individually
                }
                loadDashboardStats();
                loadAllProperties();
                alert(`${selectedProperties.size} properties deleted successfully`);
            } catch (error) {
                console.error('Bulk delete error:', error);
                alert('Some properties failed to delete');
            }
        }

        // Enhanced property management functions
        function viewPropertyDetails(propertyId) {
            const property = allPropertiesData.find(p => p._id === propertyId);
            if (!property) return;

            const modalContent = document.getElementById('propertyModalContent');
            modalContent.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Images -->
                    <div>
                        <h3 class="text-lg font-semibold mb-3">Property Images</h3>
                        <div class="grid grid-cols-2 gap-2">
                            ${(property.image_urls || []).map(url => `
                                <img src="${url}" alt="Property image" class="w-full h-32 object-cover rounded-lg">
                            `).join('') || '<p class="text-gray-500">No images available</p>'}
                        </div>
                    </div>

                    <!-- Details -->
                    <div>
                        <h3 class="text-lg font-semibold mb-3">Property Information</h3>
                        <div class="space-y-3">
                            <div><strong>Title:</strong> ${property.title}</div>
                            <div><strong>Price:</strong> ₹${formatIndianCurrency(property.price)}/month</div>
                            <div><strong>Location:</strong> ${property.city}${property.area ? `, ${property.area}` : ''}</div>
                            <div><strong>Bedrooms:</strong> ${property.bedrooms}</div>
                            <div><strong>Bathrooms:</strong> ${property.bathrooms}</div>
                            <div><strong>Furnished:</strong> ${property.furnished_status || 'Not specified'}</div>
                            <div><strong>Status:</strong> <span class="px-2 py-1 rounded-full text-xs ${getStatusBadgeClass(property.status)}">${property.status}</span></div>
                            <div><strong>Contact:</strong> ${property.phone}</div>
                            <div><strong>Created:</strong> ${new Date(property.created_at).toLocaleString()}</div>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="mt-6">
                    <h3 class="text-lg font-semibold mb-3">Description</h3>
                    <p class="text-gray-700 leading-relaxed">${property.description || 'No description provided'}</p>
                </div>

                <!-- Amenities -->
                ${property.amenities && property.amenities.length > 0 ? `
                    <div class="mt-6">
                        <h3 class="text-lg font-semibold mb-3">Amenities</h3>
                        <div class="flex flex-wrap gap-2">
                            ${property.amenities.map(amenity => `
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">${amenity}</span>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                <!-- Community Rating -->
                <div class="mt-6">
                    <h3 class="text-lg font-semibold mb-3">Community Rating</h3>
                    <div class="flex items-center space-x-4">
                        <span class="text-green-600"><i class="fas fa-thumbs-up"></i> ${property.upvotes || 0} upvotes</span>
                        <span class="text-red-600"><i class="fas fa-thumbs-down"></i> ${property.downvotes || 0} downvotes</span>
                    </div>
                </div>

                <!-- Reports -->
                ${property.reports && property.reports.length > 0 ? `
                    <div class="mt-6">
                        <h3 class="text-lg font-semibold mb-3 text-red-600">Reports (${property.reports.length})</h3>
                        <div class="space-y-2">
                            ${property.reports.map(report => `
                                <div class="p-3 bg-red-50 border border-red-200 rounded-lg">
                                    <div class="text-sm text-red-800">
                                        <strong>Reason:</strong> ${report.reason || 'No reason provided'}<br>
                                        <strong>Date:</strong> ${new Date(report.timestamp).toLocaleString()}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            `;

            document.getElementById('propertyModal').classList.remove('hidden');
        }

        function closePropertyModal() {
            document.getElementById('propertyModal').classList.add('hidden');
        }

        function editProperty(propertyId) {
            const property = allPropertiesData.find(p => p._id === propertyId);
            if (!property) return;

            const modalContent = document.getElementById('editModalContent');
            modalContent.innerHTML = `
                <form onsubmit="savePropertyEdit(event, '${propertyId}')">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                            <input type="text" name="title" value="${property.title}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Price (₹/month)</label>
                            <input type="number" name="price" value="${property.price}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                            <input type="text" name="city" value="${property.city}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Area</label>
                            <input type="text" name="area" value="${property.area || ''}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Bedrooms</label>
                            <input type="number" name="bedrooms" value="${property.bedrooms}" required min="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Bathrooms</label>
                            <input type="number" name="bathrooms" value="${property.bathrooms}" required min="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Furnished Status</label>
                            <select name="furnished_status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                                <option value="furnished" ${property.furnished_status === 'furnished' ? 'selected' : ''}>Furnished</option>
                                <option value="semi" ${property.furnished_status === 'semi' ? 'selected' : ''}>Semi Furnished</option>
                                <option value="unfurnished" ${property.furnished_status === 'unfurnished' ? 'selected' : ''}>Unfurnished</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                                <option value="pending" ${property.status === 'pending' ? 'selected' : ''}>Pending</option>
                                <option value="approved" ${property.status === 'approved' ? 'selected' : ''}>Approved</option>
                                <option value="rejected" ${property.status === 'rejected' ? 'selected' : ''}>Rejected</option>
                            </select>
                        </div>
                    </div>

                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea name="description" rows="4"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">${property.description || ''}</textarea>
                    </div>

                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeEditModal()"
                                class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-primary text-white rounded-md hover:bg-purple-700">
                            Save Changes
                        </button>
                    </div>
                </form>
            `;

            document.getElementById('editModal').classList.remove('hidden');
        }

        function closeEditModal() {
            document.getElementById('editModal').classList.add('hidden');
        }

        async function savePropertyEdit(event, propertyId) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());

            try {
                const response = await fetch(`/admin/properties/${propertyId}/edit`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    closeEditModal();
                    loadAllProperties();
                    loadDashboardStats();
                    alert('Property updated successfully');
                } else {
                    throw new Error(result.error || 'Failed to update property');
                }
            } catch (error) {
                console.error('Error updating property:', error);
                alert('Failed to update property: ' + error.message);
            }
        }

        function inspectProperty(propertyId) {
            const property = allPropertiesData.find(p => p._id === propertyId);
            if (!property) return;

            const modalContent = document.getElementById('propertyModalContent');
            modalContent.innerHTML = `
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900">Property Inspection</h3>

                    <!-- Metadata -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-3">Metadata</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div><strong>Property ID:</strong> ${property._id}</div>
                            <div><strong>Created:</strong> ${new Date(property.created_at).toLocaleString()}</div>
                            <div><strong>Updated:</strong> ${property.updated_at ? new Date(property.updated_at).toLocaleString() : 'Never'}</div>
                            <div><strong>Status:</strong> ${property.status}</div>
                        </div>
                    </div>

                    <!-- Vote History -->
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-3">Vote History</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div><strong>Total Upvotes:</strong> ${property.upvotes || 0}</div>
                            <div><strong>Total Downvotes:</strong> ${property.downvotes || 0}</div>
                            <div><strong>Net Score:</strong> ${(property.upvotes || 0) - (property.downvotes || 0)}</div>
                            <div><strong>Total Votes:</strong> ${(property.upvotes || 0) + (property.downvotes || 0)}</div>
                        </div>
                        ${property.votes && property.votes.length > 0 ? `
                            <div class="mt-3">
                                <strong>Recent Votes:</strong>
                                <div class="max-h-32 overflow-y-auto mt-2 space-y-1">
                                    ${property.votes.slice(-5).map(vote => `
                                        <div class="text-xs p-2 bg-white rounded border">
                                            <span class="${vote.vote_type === 'up' ? 'text-green-600' : 'text-red-600'}">
                                                ${vote.vote_type === 'up' ? '👍' : '👎'} ${vote.vote_type}vote
                                            </span>
                                            from ${vote.ip_address} at ${new Date(vote.timestamp).toLocaleString()}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>

                    <!-- Reports -->
                    ${property.reports && property.reports.length > 0 ? `
                        <div class="bg-red-50 p-4 rounded-lg">
                            <h4 class="font-semibold mb-3 text-red-800">Reports (${property.reports.length})</h4>
                            <div class="space-y-2">
                                ${property.reports.map(report => `
                                    <div class="text-sm p-2 bg-white rounded border border-red-200">
                                        <div><strong>Reason:</strong> ${report.reason || 'No reason provided'}</div>
                                        <div><strong>IP:</strong> ${report.ip_address}</div>
                                        <div><strong>Date:</strong> ${new Date(report.timestamp).toLocaleString()}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : '<div class="bg-green-50 p-4 rounded-lg text-green-800">No reports for this property</div>'}

                    <!-- Images Analysis -->
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h4 class="font-semibold mb-3">Images Analysis</h4>
                        <div class="text-sm">
                            <div><strong>Total Images:</strong> ${property.image_urls ? property.image_urls.length : 0}</div>
                            ${property.image_urls && property.image_urls.length > 0 ? `
                                <div class="mt-2">
                                    <strong>Image URLs:</strong>
                                    <div class="max-h-32 overflow-y-auto mt-1">
                                        ${property.image_urls.map((url, index) => `
                                            <div class="text-xs p-1 bg-white rounded border mb-1">
                                                Image ${index + 1}: <a href="${url}" target="_blank" class="text-blue-600 hover:underline">${url}</a>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('propertyModal').classList.remove('hidden');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                window.location.href = '/admin/logout';
            }
        }
    </script>
</body>
</html>
