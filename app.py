import os
import logging
import io
import json
from datetime import datetime, timezone, timedelta
from functools import wraps
from bson import ObjectId
from bson.errors import InvalidId
import cloudinary
import cloudinary.uploader
from flask import Flask, request, jsonify, render_template, abort, redirect, session, Response
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_talisman import Talisman
import hashlib
import time
from collections import defaultdict, deque
from pymongo import MongoClient, ASCENDING, TEXT
from dotenv import load_dotenv
import re
from PIL import Image, ImageOps

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize CORS
CORS(app, origins=os.getenv('ALLOWED_ORIGINS', '*').split(','))

# Security Configuration
is_production = os.getenv('FLASK_ENV') == 'production'
app.config.update(
    SESSION_COOKIE_SECURE=is_production,  # True in production with HTTPS
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=3600,  # 1 hour
    MAX_CONTENT_LENGTH=50 * 1024 * 1024,  # 50MB max request size
)

# Initialize Talisman for security headers
csp = {
    'default-src': "'self'",
    'script-src': "'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://kit.fontawesome.com https://cdn.tailwindcss.com",
    'style-src': "'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com https://cdn.tailwindcss.com",
    'font-src': "'self' https://fonts.gstatic.com https://ka-f.fontawesome.com https://cdnjs.cloudflare.com",
    'img-src': "'self' data: https: http:",
    'connect-src': "'self' https:",
    'frame-ancestors': "'none'",
    'base-uri': "'self'",
    'form-action': "'self'"
}

# Initialize Talisman for production security
if is_production:
    talisman = Talisman(
        app,
        force_https=True,  # Force HTTPS in production
        strict_transport_security=True,
        content_security_policy=csp,
        referrer_policy='strict-origin-when-cross-origin',
        feature_policy={
            'geolocation': "'none'",
            'camera': "'none'",
            'microphone': "'none'"
        }
    )

# Advanced DDoS Protection
class DDoSProtection:
    def __init__(self):
        self.request_counts = defaultdict(deque)
        self.blocked_ips = defaultdict(float)
        self.suspicious_patterns = defaultdict(int)

    def is_blocked(self, ip):
        """Check if IP is currently blocked"""
        if ip in self.blocked_ips:
            if time.time() < self.blocked_ips[ip]:
                return True
            else:
                del self.blocked_ips[ip]
        return False

    def check_request(self, ip, endpoint):
        """Check if request should be allowed"""
        current_time = time.time()

        # Clean old requests (older than 1 minute)
        while self.request_counts[ip] and self.request_counts[ip][0] < current_time - 60:
            self.request_counts[ip].popleft()

        # Add current request
        self.request_counts[ip].append(current_time)

        # Check for DDoS patterns
        requests_per_minute = len(self.request_counts[ip])

        # Aggressive rate limiting for Kolkata startup
        if requests_per_minute > 60:  # More than 60 requests per minute
            self.block_ip(ip, 1800)  # Block for 30 minutes
            return False
        elif requests_per_minute > 30:  # More than 30 requests per minute
            self.block_ip(ip, 300)   # Block for 5 minutes
            return False
        elif requests_per_minute > 20:  # More than 20 requests per minute
            self.suspicious_patterns[ip] += 1
            if self.suspicious_patterns[ip] > 2:
                self.block_ip(ip, 600)  # Block for 10 minutes
                return False

        return True

    def block_ip(self, ip, duration):
        """Block IP for specified duration"""
        self.blocked_ips[ip] = time.time() + duration
        logger.warning(f"🚨 SECURITY: Blocked IP {ip} for {duration} seconds due to suspicious activity")

ddos_protection = DDoSProtection()

# Initialize rate limiter with enhanced limits
limiter = Limiter(
    key_func=get_remote_address,
    app=app,
    default_limits=["500 per day", "100 per hour", "20 per minute"],
    headers_enabled=True
)

# Configure Cloudinary
cloudinary.config(
    cloud_name=os.getenv('CLOUDINARY_CLOUD_NAME'),
    api_key=os.getenv('CLOUDINARY_API_KEY'),
    api_secret=os.getenv('CLOUDINARY_API_SECRET'),
    secure=True
)

# MongoDB connection
try:
    client = MongoClient(os.getenv('MONGO_URI', 'mongodb://localhost:27017/'))
    db = client[os.getenv('MONGO_DB_NAME', 'eznest')]
    properties_collection = db.properties

    # Create comprehensive text index for advanced search with error handling
    try:
        # First, try to drop existing conflicting text index
        try:
            existing_indexes = list(properties_collection.list_indexes())
            for index in existing_indexes:
                if index.get('key', {}).get('_fts') == 'text':
                    properties_collection.drop_index(index['name'])
                    logger.info(f"Dropped existing text index: {index['name']}")
        except Exception as drop_error:
            logger.warning(f"Could not drop existing text index: {drop_error}")

        # Create new enhanced text index
        properties_collection.create_index([
            ("title", TEXT),
            ("description", TEXT),
            ("city", TEXT),
            ("area", TEXT),
            ("amenities", TEXT)
        ], weights={
            "title": 10,      # Highest weight for title
            "city": 8,        # High weight for city
            "area": 6,        # Medium-high weight for area/locality
            "description": 4,  # Medium weight for description
            "amenities": 2    # Lower weight for amenities
        }, name="enhanced_search_index")
        logger.info("Enhanced text search index created successfully")
    except Exception as text_error:
        logger.warning(f"Could not create enhanced text index: {text_error}")
        # Continue without text index - basic search will still work

    # Create individual indexes for filtering (with error handling)
    try:
        properties_collection.create_index("city")
        properties_collection.create_index("area")
        properties_collection.create_index("price")
        properties_collection.create_index("bedrooms")
        properties_collection.create_index("bathrooms")
        properties_collection.create_index("furnished_status")
        properties_collection.create_index("status")
        properties_collection.create_index("created_at")
        logger.info("Individual indexes created successfully")
    except Exception as index_error:
        logger.warning(f"Could not create some individual indexes: {index_error}")

    # Compound indexes for common search patterns (with error handling)
    try:
        properties_collection.create_index([("city", 1), ("bedrooms", 1), ("price", 1)])
        properties_collection.create_index([("status", 1), ("created_at", -1)])
        logger.info("Compound indexes created successfully")
    except Exception as compound_error:
        logger.warning(f"Could not create compound indexes: {compound_error}")

    logger.info("MongoDB connected and indexes created successfully")
except Exception as e:
    logger.error(f"MongoDB connection failed: {e}")
    db = None

# Admin authentication decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Check session authentication first
        if session.get('admin_authenticated'):
            return f(*args, **kwargs)

        # Fallback to HTTP Basic Auth for API calls
        auth = request.authorization
        admin_user = os.getenv('ADMIN_USER', 'admin')
        admin_pass = os.getenv('ADMIN_PASS', 'admin123')

        if not auth or auth.username != admin_user or auth.password != admin_pass:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    return decorated_function

# Security middleware
@app.before_request
def security_checks():
    """Perform security checks before each request"""
    client_ip = get_remote_address()

    # Skip security checks for static files and localhost during development
    if request.endpoint == 'static' or client_ip in ['127.0.0.1', 'localhost']:
        return

    # DDoS Protection (disabled for localhost during development)
    # if ddos_protection.is_blocked(client_ip):
    #     logger.warning(f"🚨 SECURITY: Blocked request from {client_ip}")
    #     abort(429)  # Too Many Requests

    # if not ddos_protection.check_request(client_ip, request.endpoint):
    #     abort(429)

    # Block common attack patterns
    user_agent = request.headers.get('User-Agent', '').lower()
    suspicious_agents = ['sqlmap', 'nikto', 'nmap', 'masscan', 'zap', 'bot', 'crawler']
    if any(agent in user_agent for agent in suspicious_agents):
        logger.warning(f"🚨 SECURITY: Blocked suspicious user agent from {client_ip}: {user_agent}")
        abort(403)

    # Check for SQL injection patterns in query parameters
    for param_value in request.args.values():
        if any(pattern in param_value.lower() for pattern in ['union select', 'drop table', 'insert into', '1=1', 'or 1=1', 'script>', '<iframe']):
            logger.warning(f"🚨 SECURITY: Blocked injection attempt from {client_ip}")
            abort(403)

    # Validate Content-Type for POST requests
    if request.method == 'POST' and request.endpoint not in ['static', 'admin_login']:
        content_type = request.headers.get('Content-Type', '')
        if not any(ct in content_type for ct in ['application/json', 'multipart/form-data', 'application/x-www-form-urlencoded']):
            abort(400)

# Input validation and sanitization
def sanitize_input(text):
    """Sanitize user input to prevent XSS and injection attacks"""
    if not text:
        return text

    # Remove potentially dangerous characters
    dangerous_chars = ['<script', '</script', 'javascript:', 'data:', 'vbscript:', 'onload=', 'onerror=']
    text_lower = text.lower()
    for char in dangerous_chars:
        if char in text_lower:
            logger.warning(f"🚨 SECURITY: Blocked dangerous input: {text[:50]}...")
            return ""

    return text.strip()

def validate_indian_phone(phone):
    """Validate Indian phone number format"""
    # Remove all non-digit characters except +
    clean_phone = re.sub(r'[^\d\+]', '', phone)

    # Check for valid Indian phone number patterns
    patterns = [
        r'^\+91[6-9]\d{9}$',  # +91 followed by 10 digits starting with 6-9
        r'^[6-9]\d{9}$',      # 10 digits starting with 6-9
        r'^0[6-9]\d{9}$'      # 0 followed by 10 digits starting with 6-9
    ]

    return any(re.match(pattern, clean_phone) for pattern in patterns)

# Utility functions
def check_admin_auth(username, password):
    """Check admin authentication credentials"""
    admin_user = os.getenv('ADMIN_USER', 'admin')
    admin_pass = os.getenv('ADMIN_PASS', 'admin123')
    return username == admin_user and password == admin_pass

def validate_property_data(data):
    """Validate property form data - only 4 fields required, others optional"""
    # Only validate required fields: title, city, price, phone
    required_fields = ['title', 'city', 'price', 'phone']

    for field in required_fields:
        if not data.get(field):
            return False, f"Missing required field: {field}"

        # Sanitize input
        data[field] = sanitize_input(str(data[field]))

    try:
        price = float(data['price'])
        if price <= 0:
            return False, "Monthly rent must be a positive number"
    except (ValueError, TypeError):
        return False, "Invalid monthly rent value"

    # Validate Indian phone number
    if not validate_indian_phone(data['phone']):
        return False, "Please enter a valid Indian WhatsApp number (e.g., +91 98765 43210)"

    # Validate title length
    if len(data['title']) < 10:
        return False, "Property title must be at least 10 characters long"

    # Optional field validation (if provided)
    if data.get('bedrooms'):
        try:
            bedrooms = int(data['bedrooms'])
            if bedrooms <= 0:
                return False, "Number of bedrooms must be positive"
        except (ValueError, TypeError):
            return False, "Invalid number of bedrooms"

    if data.get('bathrooms'):
        try:
            bathrooms = int(data['bathrooms'])
            if bathrooms <= 0:
                return False, "Number of bathrooms must be positive"
        except (ValueError, TypeError):
            return False, "Invalid number of bathrooms"

    # Validate description length if provided
    if data.get('description') and len(data['description']) < 20:
        return False, "Description must be at least 20 characters long if provided"

    return True, None

def generate_slug(title):
    """Generate SEO-friendly slug from title"""
    slug = re.sub(r'[^a-zA-Z0-9\s-]', '', title.lower())
    slug = re.sub(r'\s+', '-', slug)
    slug = re.sub(r'-+', '-', slug)
    return slug.strip('-')

def compress_image(file, max_width=1200, max_height=900, quality=85):
    """Compress and optimize image for web display"""
    try:
        # Reset file pointer
        file.seek(0)

        # Open image with PIL
        image = Image.open(file)

        # Convert RGBA to RGB if necessary (for JPEG compatibility)
        if image.mode in ('RGBA', 'LA', 'P'):
            # Create a white background
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'P':
                image = image.convert('RGBA')
            background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
            image = background
        elif image.mode != 'RGB':
            image = image.convert('RGB')

        # Auto-rotate based on EXIF data
        image = ImageOps.exif_transpose(image)

        # Calculate new dimensions while maintaining aspect ratio
        width, height = image.size
        if width > max_width or height > max_height:
            # Calculate scaling factor
            scale_w = max_width / width
            scale_h = max_height / height
            scale = min(scale_w, scale_h)

            new_width = int(width * scale)
            new_height = int(height * scale)

            # Resize image with high-quality resampling
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # Save compressed image to BytesIO
        output = io.BytesIO()
        image.save(output, format='JPEG', quality=quality, optimize=True)
        output.seek(0)

        # Calculate compression ratio
        original_size = file.tell() if hasattr(file, 'tell') else len(file.read())
        file.seek(0)
        compressed_size = len(output.getvalue())
        compression_ratio = (1 - compressed_size / original_size) * 100 if original_size > 0 else 0

        logger.info(f"Image compressed: {original_size} bytes -> {compressed_size} bytes ({compression_ratio:.1f}% reduction)")

        return output

    except Exception as e:
        logger.error(f"Image compression failed: {e}")
        # Return original file if compression fails
        file.seek(0)
        return file

def upload_images_to_cloudinary(files, property_id):
    """Upload compressed images to Cloudinary and return URLs"""
    image_urls = []

    for i, file in enumerate(files):
        try:
            # Compress image before upload
            compressed_file = compress_image(file)

            # Upload compressed image to Cloudinary
            result = cloudinary.uploader.upload(
                compressed_file,
                folder=f"properties/{property_id}",
                public_id=f"image_{i}_{int(datetime.now().timestamp())}",
                transformation=[
                    {'width': 800, 'height': 600, 'crop': 'fill', 'quality': 'auto'},
                    {'fetch_format': 'auto'}
                ],
                resource_type="auto",
                use_filename=False,
                unique_filename=True
            )
            image_urls.append(result['secure_url'])
            logger.info(f"Successfully uploaded compressed image {i} for property {property_id}")

        except Exception as e:
            logger.error(f"Failed to upload image {i}: {e}")
            # Try alternative upload method with original file
            try:
                file.seek(0)
                result = cloudinary.uploader.upload(
                    file,
                    folder=f"properties/{property_id}",
                    public_id=f"image_{i}_fallback_{int(datetime.now().timestamp())}",
                    resource_type="auto"
                )
                image_urls.append(result['secure_url'])
                logger.info(f"Successfully uploaded image {i} using fallback method")
            except Exception as e2:
                logger.error(f"Fallback upload also failed for image {i}: {e2}")
                continue

    return image_urls

# Template routes
@app.route('/')
def index():
    """Serve the main index page"""
    return render_template('index.html')

@app.route('/post-property')
def post_property_page():
    """Serve the post property page"""
    return render_template('post_property.html')

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """Handle admin login"""
    if request.method == 'POST':
        # Handle login form submission
        data = request.get_json() if request.is_json else request.form
        username = data.get('username', '')
        password = data.get('password', '')

        if check_admin_auth(username, password):
            session['admin_authenticated'] = True
            session['admin_user'] = username
            return jsonify({'success': True, 'redirect': '/admin/panel'})
        else:
            return jsonify({'success': False, 'error': 'Invalid credentials'}), 401

    # GET request - serve login page
    return render_template('admin_login.html')

@app.route('/admin/panel')
def admin_panel():
    """Serve the admin panel page"""
    # Check if user is authenticated via session
    if not session.get('admin_authenticated'):
        return redirect('/admin/login')
    return render_template('admin.html')

@app.route('/admin/logout')
def admin_logout():
    """Handle admin logout"""
    session.pop('admin_authenticated', None)
    session.pop('admin_user', None)
    return redirect('/admin/login')

# SEO and Sitemap Routes
@app.route('/sitemap.xml')
def sitemap():
    """Generate dynamic sitemap for SEO"""
    try:
        # Get base URL from request
        base_url = request.url_root.rstrip('/')

        # Start building sitemap XML
        sitemap_xml = '''<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">'''

        # Static pages
        static_pages = [
            {
                'url': f'{base_url}/',
                'lastmod': '2025-05-28',
                'changefreq': 'daily',
                'priority': '1.0'
            },
            {
                'url': f'{base_url}/post-property',
                'lastmod': '2025-05-28',
                'changefreq': 'weekly',
                'priority': '0.8'
            },
            {
                'url': f'{base_url}/admin/login',
                'lastmod': '2025-05-28',
                'changefreq': 'monthly',
                'priority': '0.3'
            }
        ]

        # Add static pages to sitemap
        for page in static_pages:
            sitemap_xml += f'''
  <url>
    <loc>{page['url']}</loc>
    <lastmod>{page['lastmod']}</lastmod>
    <changefreq>{page['changefreq']}</changefreq>
    <priority>{page['priority']}</priority>
  </url>'''

        # Add dynamic property pages (approved properties only)
        try:
            properties = properties_collection.find(
                {'status': 'approved'},
                {'_id': 1, 'slug': 1, 'updated_at': 1}
            ).limit(1000)  # Limit for performance

            for prop in properties:
                property_url = f"{base_url}/property/{prop['slug']}-{str(prop['_id'])}"
                lastmod = prop.get('updated_at', datetime.now(timezone.utc)).strftime('%Y-%m-%d')

                sitemap_xml += f'''
  <url>
    <loc>{property_url}</loc>
    <lastmod>{lastmod}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>'''
        except Exception as e:
            logger.warning(f"Could not add property URLs to sitemap: {e}")

        # Close sitemap XML
        sitemap_xml += '''
</urlset>'''

        return Response(sitemap_xml, mimetype='application/xml')

    except Exception as e:
        logger.error(f"Error generating sitemap: {e}")
        # Fallback to static sitemap
        try:
            with open('sitemap.xml', 'r') as f:
                return Response(f.read(), mimetype='application/xml')
        except:
            return Response('<?xml version="1.0" encoding="UTF-8"?><urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"></urlset>',
                          mimetype='application/xml')

@app.route('/robots.txt')
def robots():
    """Generate robots.txt for SEO"""
    base_url = request.url_root.rstrip('/')
    robots_txt = f'''User-agent: *
Allow: /
Allow: /post-property
Disallow: /admin/
Disallow: /api/

Sitemap: {base_url}/sitemap.xml'''

    return Response(robots_txt, mimetype='text/plain')

@app.route('/placeholder.svg')
def placeholder_svg():
    """Generate placeholder SVG with dynamic dimensions"""
    width = request.args.get('width', '400')
    height = request.args.get('height', '300')

    svg_content = f'''<svg width="{width}" height="{height}" xmlns="http://www.w3.org/2000/svg">
  <rect width="100%" height="100%" fill="#f3f4f6"/>
  <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="#6b7280" text-anchor="middle" dy=".3em">
    Property Image
  </text>
</svg>'''

    return Response(svg_content, mimetype='image/svg+xml')

# Robust Universal Search Algorithm
def parse_search_query(search_query):
    """
    Robust search parser that works for all cities and addresses
    Examples:
    - "kolkata" -> searches city, address, title, description
    - "2bhk mumbai" -> bedrooms: 2, searches for mumbai
    - "furnished flat delhi" -> furnished_status: furnished, searches for delhi
    - "park street" -> searches in all text fields
    - "bangalore koramangala" -> searches for bangalore and koramangala
    """
    if not search_query or not search_query.strip():
        return {}

    search_query = search_query.lower().strip()
    filter_conditions = {}
    remaining_terms = []

    # Extract bedroom information
    bedroom_patterns = [
        r'(\d+)\s*bhk\b', r'(\d+)\s*bedroom\b', r'(\d+)\s*bed\b',
        r'(\d+)\s*room\b', r'(\d+)bhk\b', r'(\d+)bedroom\b'
    ]

    for pattern in bedroom_patterns:
        match = re.search(pattern, search_query)
        if match:
            bedrooms = int(match.group(1))
            filter_conditions['bedrooms'] = bedrooms
            # Remove the matched pattern from search query
            search_query = re.sub(pattern, '', search_query).strip()
            break

    # Extract bathroom information
    bathroom_patterns = [r'(\d+)\s*bathroom\b', r'(\d+)\s*bath\b']
    for pattern in bathroom_patterns:
        match = re.search(pattern, search_query)
        if match:
            bathrooms = int(match.group(1))
            filter_conditions['bathrooms'] = bathrooms
            search_query = re.sub(pattern, '', search_query).strip()
            break

    # Extract furnished status
    if re.search(r'\b(fully\s+)?furnished\b', search_query):
        filter_conditions['furnished_status'] = 'furnished'
        search_query = re.sub(r'\b(fully\s+)?furnished\b', '', search_query).strip()
    elif re.search(r'\bsemi[\s-]?furnished\b', search_query):
        filter_conditions['furnished_status'] = 'semi'
        search_query = re.sub(r'\bsemi[\s-]?furnished\b', '', search_query).strip()
    elif re.search(r'\bunfurnished\b', search_query):
        filter_conditions['furnished_status'] = 'none'
        search_query = re.sub(r'\bunfurnished\b', '', search_query).strip()

    # Extract price range
    price_patterns = [
        (r'under\s+(\d+)', 'max'),
        (r'below\s+(\d+)', 'max'),
        (r'less\s+than\s+(\d+)', 'max'),
        (r'above\s+(\d+)', 'min'),
        (r'over\s+(\d+)', 'min'),
        (r'more\s+than\s+(\d+)', 'min'),
        (r'(\d+)\s*to\s*(\d+)', 'range'),
        (r'(\d+)\s*-\s*(\d+)', 'range')
    ]

    for pattern, price_type in price_patterns:
        match = re.search(pattern, search_query)
        if match:
            if price_type == 'max':
                filter_conditions['price'] = {'$lte': int(match.group(1))}
            elif price_type == 'min':
                filter_conditions['price'] = {'$gte': int(match.group(1))}
            elif price_type == 'range':
                min_price = int(match.group(1))
                max_price = int(match.group(2))
                filter_conditions['price'] = {'$gte': min_price, '$lte': max_price}
            search_query = re.sub(pattern, '', search_query).strip()
            break

    # Clean up the remaining search query
    search_query = ' '.join(search_query.split())  # Remove extra spaces

    # If there's still text left, create a comprehensive search
    if search_query:
        # Create OR conditions for comprehensive search across all relevant fields
        # Search for the full query in each field
        or_conditions = [
            {'city': {'$regex': search_query, '$options': 'i'}},
            {'address': {'$regex': search_query, '$options': 'i'}},
            {'title': {'$regex': search_query, '$options': 'i'}},
            {'description': {'$regex': search_query, '$options': 'i'}},
            {'area': {'$regex': search_query, '$options': 'i'}}
        ]

        # Also search for individual terms if it's a multi-word query
        search_terms = search_query.split()
        if len(search_terms) > 1:
            for term in search_terms:
                if len(term) > 2:  # Only search for terms longer than 2 characters
                    or_conditions.extend([
                        {'city': {'$regex': term, '$options': 'i'}},
                        {'address': {'$regex': term, '$options': 'i'}},
                        {'title': {'$regex': term, '$options': 'i'}},
                        {'description': {'$regex': term, '$options': 'i'}},
                        {'area': {'$regex': term, '$options': 'i'}}
                    ])

        filter_conditions['$or'] = or_conditions

    return filter_conditions

# API Routes
@app.route('/api/properties', methods=['GET'])
def get_properties():
    """Get properties with filtering, pagination, search, and admin access"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)  # Increased max for admin
        admin_access = request.args.get('admin', '').lower() == 'true'

        # Admin-specific parameters
        status_filter = request.args.get('status', '').strip()
        date_filter = request.args.get('date_filter', '').strip()
        sort_by = request.args.get('sort_by', 'created_at_desc').strip()

        # Filters
        city = request.args.get('city', '').strip()
        min_price = request.args.get('minPrice', type=float)
        max_price = request.args.get('maxPrice', type=float)
        search = request.args.get('search', '').strip()
        bedrooms = request.args.get('bedrooms', type=int)
        bathrooms = request.args.get('bathrooms', type=int)
        furnished = request.args.get('furnished', '').strip()

        # Build MongoDB filter
        if admin_access:
            # Check if admin is authenticated
            if not session.get('admin_authenticated'):
                return jsonify({'success': False, 'error': 'Admin access required'}), 403
            # Admin can see all properties regardless of status
            filter_query = {}

            # Apply admin-specific status filter
            if status_filter and status_filter in ['pending', 'approved', 'rejected']:
                filter_query['status'] = status_filter
        else:
            # Regular users only see approved properties
            filter_query = {'status': 'approved'}

        # Apply date filter for admin
        if admin_access and date_filter:
            try:
                now = datetime.now()
                if date_filter == 'today':
                    start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
                    filter_query['created_at'] = {'$gte': start_date}
                elif date_filter == 'week':
                    start_date = now - timedelta(days=7)
                    filter_query['created_at'] = {'$gte': start_date}
                elif date_filter == 'month':
                    start_date = now - timedelta(days=30)
                    filter_query['created_at'] = {'$gte': start_date}
            except Exception as date_error:
                logger.error(f"Date filter error: {date_error}")
                # Skip date filtering if there's an error

        # Simplified filtering for now - will add backward compatibility later
        if city:
            filter_query['$or'] = [
                {'city': {'$regex': city, '$options': 'i'}},
                {'location': {'$regex': city, '$options': 'i'}}
            ]

        if min_price is not None or max_price is not None:
            price_filter = {}
            if min_price is not None:
                price_filter['$gte'] = min_price
            if max_price is not None:
                price_filter['$lte'] = max_price
            # For now, just use the new field name
            filter_query['price'] = price_filter

        if search:
            # Enhanced search with smart parsing
            try:
                search_terms = parse_search_query(search)
                filter_query.update(search_terms)
                # Debug logging
                logger.info(f"Search query: '{search}' -> Filter: {search_terms}")
            except Exception as search_error:
                logger.error(f"Search parsing error: {search_error}")
                # Fallback to simple search
                filter_query['$or'] = [
                    {'title': {'$regex': search, '$options': 'i'}},
                    {'city': {'$regex': search, '$options': 'i'}},
                    {'description': {'$regex': search, '$options': 'i'}}
                ]

        if bedrooms is not None:
            filter_query['bedrooms'] = bedrooms

        if bathrooms is not None:
            filter_query['bathrooms'] = bathrooms

        if furnished and furnished in ['furnished', 'semi', 'none']:
            filter_query['furnished_status'] = furnished

        # Calculate skip value
        skip = (page - 1) * per_page

        # Get total count (with timeout for large collections)
        try:
            total = properties_collection.count_documents(filter_query)
        except Exception as count_error:
            logger.warning(f"Count operation timed out, using estimated count: {count_error}")
            total = properties_collection.estimated_document_count()

        # Determine sort order
        sort_field = 'created_at'
        sort_direction = -1  # Default: newest first

        if admin_access and sort_by:
            if sort_by == 'created_at_asc':
                sort_direction = 1
            elif sort_by == 'price_desc':
                sort_field = 'price'
                sort_direction = -1
            elif sort_by == 'price_asc':
                sort_field = 'price'
                sort_direction = 1
            elif sort_by == 'votes_desc':
                # For votes, we'll sort by a computed field
                sort_field = 'upvotes'
                sort_direction = -1

        # Get properties with pagination and sorting
        cursor = properties_collection.find(filter_query).skip(skip).limit(per_page)
        cursor = cursor.sort(sort_field, sort_direction)

        properties = []
        for prop in cursor:
            prop['_id'] = str(prop['_id'])

            # Handle field mapping for backward compatibility
            # Map old field names to new field names
            if 'rent' in prop and 'price' not in prop:
                prop['price'] = prop['rent']
            if 'location' in prop and 'city' not in prop:
                prop['city'] = prop['location']
            if 'ownerPhone' in prop and 'phone' not in prop:
                prop['phone'] = prop['ownerPhone']
            if 'imageUrls' in prop and 'image_urls' not in prop:
                prop['image_urls'] = prop['imageUrls']

            # Ensure required fields have default values
            prop.setdefault('price', 0)
            prop.setdefault('city', 'Not specified')
            prop.setdefault('phone', 'Not provided')
            prop.setdefault('status', 'pending')
            prop.setdefault('upvotes', 0)
            prop.setdefault('downvotes', 0)
            prop.setdefault('reports', [])
            prop.setdefault('created_at', datetime.now())

            # Add computed fields for admin
            if admin_access:
                prop['vote_score'] = (prop.get('upvotes', 0) - prop.get('downvotes', 0))
                prop['report_count'] = len(prop.get('reports', []))
            properties.append(prop)

        return jsonify({
            'success': True,
            'data': properties,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page,
            'has_next': page * per_page < total,
            'has_prev': page > 1
        })

    except Exception as e:
        logger.error(f"Error fetching properties: {e}")
        logger.error(f"Error details: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': 'Failed to fetch properties'}), 500

@app.route('/api/properties', methods=['POST'])
@limiter.limit("5 per minute")
def create_property():
    """Create a new property listing"""
    try:
        # Validate form data
        form_data = request.form.to_dict()
        is_valid, error_msg = validate_property_data(form_data)

        if not is_valid:
            return jsonify({'success': False, 'error': error_msg}), 400

        # Handle image uploads
        image_files = []
        for key in request.files:
            if key.startswith('image_'):
                file = request.files[key]
                if file and file.filename:
                    # Validate file type
                    if not file.content_type.startswith('image/'):
                        return jsonify({'success': False, 'error': f'Invalid file type: {file.filename}'}), 400

                    # Validate file size (5MB max per image)
                    file.seek(0, 2)  # Seek to end
                    file_size = file.tell()
                    file.seek(0)  # Reset to beginning

                    if file_size > 5 * 1024 * 1024:
                        return jsonify({'success': False, 'error': f'File too large: {file.filename}'}), 400

                    image_files.append(file)

        # Temporarily allow properties without images for testing
        # if not image_files:
        #     return jsonify({'success': False, 'error': 'At least one image is required'}), 400

        # Create property document with required and optional fields
        property_doc = {
            # Required fields
            'title': form_data['title'].strip(),
            'city': form_data['city'].strip(),
            'price': float(form_data['price']),
            'phone': re.sub(r'[^\d\+]', '', form_data['phone']),

            # Optional fields with defaults
            'description': form_data.get('description', '').strip() or f"{form_data['title']} - Contact for more details",
            'address': form_data.get('address', '').strip() or 'Address not specified',
            'bedrooms': int(form_data.get('bedrooms', 1)) if form_data.get('bedrooms') else 1,
            'bathrooms': int(form_data.get('bathrooms', 1)) if form_data.get('bathrooms') else 1,
            'furnished_status': form_data.get('furnished_status', 'none'),
            'amenities': [],

            # System fields
            'upvotes': 0,
            'downvotes': 0,
            'reports': [],
            'status': 'pending',
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc),
            'slug': generate_slug(form_data['title'])
        }

        # Handle amenities if provided
        if form_data.get('amenities'):
            if isinstance(form_data['amenities'], str):
                # If it's a JSON string, parse it
                try:
                    amenities = json.loads(form_data['amenities'])
                    property_doc['amenities'] = amenities if isinstance(amenities, list) else []
                except:
                    # If it's a comma-separated string
                    property_doc['amenities'] = [a.strip() for a in form_data['amenities'].split(',') if a.strip()]
            elif isinstance(form_data['amenities'], list):
                property_doc['amenities'] = form_data['amenities']

        # Insert property to get ID
        result = properties_collection.insert_one(property_doc)
        property_id = str(result.inserted_id)

        # Upload images to Cloudinary (if any)
        image_urls = []
        if image_files:
            try:
                image_urls = upload_images_to_cloudinary(image_files, property_id)
                if not image_urls:
                    logger.warning(f"No images uploaded successfully for property {property_id}")
            except Exception as e:
                logger.error(f"Error uploading images for property {property_id}: {e}")

        # Update property with image URLs (empty array if no images)
        properties_collection.update_one(
            {'_id': result.inserted_id},
            {'$set': {'image_urls': image_urls}}
        )

        logger.info(f"Property created successfully: {property_id}")

        return jsonify({
            'success': True,
            'property_id': property_id,
            'message': 'Property submitted successfully and is pending review'
        }), 201

    except Exception as e:
        logger.error(f"Error creating property: {e}")
        return jsonify({'success': False, 'error': 'Failed to create property'}), 500

@app.route('/api/vote', methods=['POST'])
@limiter.limit("3 per minute")  # Reduced from 5 to 3 votes per minute
def vote_property():
    """Handle property voting with strict IP-based rate limiting"""
    try:
        data = request.get_json()

        if not data or 'property_id' not in data or 'vote_type' not in data:
            return jsonify({'success': False, 'error': 'Missing required fields'}), 400

        property_id = data['property_id']
        vote_type = data['vote_type']
        client_ip = get_remote_address()

        if vote_type not in ['up', 'down']:
            return jsonify({'success': False, 'error': 'Invalid vote type'}), 400

        try:
            object_id = ObjectId(property_id)
        except InvalidId:
            return jsonify({'success': False, 'error': 'Invalid property ID'}), 400

        # Check if this IP has already voted on this property recently (within 24 hours)
        recent_vote_check = properties_collection.find_one({
            '_id': object_id,
            'votes': {
                '$elemMatch': {
                    'ip_address': client_ip,
                    'timestamp': {
                        '$gte': datetime.now(timezone.utc) - timedelta(hours=24)
                    }
                }
            }
        })

        if recent_vote_check:
            return jsonify({
                'success': False,
                'error': 'You have already voted on this property recently. Please wait 24 hours before voting again.'
            }), 429

        # Create vote record
        vote_record = {
            'ip_address': client_ip,
            'vote_type': vote_type,
            'timestamp': datetime.now(timezone.utc),
            'user_agent': request.headers.get('User-Agent', '')[:200]  # Limit length
        }

        # Update vote count and add vote record atomically
        update_field = 'upvotes' if vote_type == 'up' else 'downvotes'
        result = properties_collection.update_one(
            {'_id': object_id, 'status': 'approved'},
            {
                '$inc': {update_field: 1},
                '$push': {'votes': vote_record}
            }
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found or not approved'}), 404

        # Get updated counts
        property_doc = properties_collection.find_one({'_id': object_id}, {'upvotes': 1, 'downvotes': 1})

        logger.info(f"Vote recorded: {property_id} - {vote_type} from IP: {client_ip}")

        return jsonify({
            'success': True,
            'upvotes': property_doc['upvotes'],
            'downvotes': property_doc['downvotes'],
            'message': 'Vote recorded successfully'
        })

    except Exception as e:
        logger.error(f"Error recording vote: {e}")
        return jsonify({'success': False, 'error': 'Failed to record vote'}), 500

@app.route('/api/report', methods=['POST'])
@limiter.limit("5 per minute")
def report_property():
    """Handle property reports"""
    try:
        data = request.get_json()

        if not data or 'property_id' not in data:
            return jsonify({'success': False, 'error': 'Missing property ID'}), 400

        property_id = data['property_id']
        reason = data.get('reason', 'User reported content')

        try:
            object_id = ObjectId(property_id)
        except InvalidId:
            return jsonify({'success': False, 'error': 'Invalid property ID'}), 400

        # Add report to property
        report_doc = {
            'reason': reason,
            'timestamp': datetime.now(timezone.utc),
            'ip_address': get_remote_address()
        }

        result = properties_collection.update_one(
            {'_id': object_id},
            {'$push': {'reports': report_doc}}
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        logger.info(f"Property reported: {property_id} - {reason}")

        return jsonify({
            'success': True,
            'message': 'Report submitted successfully'
        })

    except Exception as e:
        logger.error(f"Error submitting report: {e}")
        return jsonify({'success': False, 'error': 'Failed to submit report'}), 500

# Admin Panel Routes
@app.route('/admin')
@admin_required
def admin_dashboard():
    """Admin dashboard"""
    try:
        # Get pending properties count
        pending_count = properties_collection.count_documents({'status': 'pending'})

        # Get reported properties count
        reported_count = properties_collection.count_documents({'reports.0': {'$exists': True}})

        # Get total properties count
        total_count = properties_collection.count_documents({})

        # Get approved properties count
        approved_count = properties_collection.count_documents({'status': 'approved'})

        return jsonify({
            'success': True,
            'stats': {
                'pending': pending_count,
                'reported': reported_count,
                'total': total_count,
                'approved': approved_count
            }
        })

    except Exception as e:
        logger.error(f"Error loading admin dashboard: {e}")
        return jsonify({'success': False, 'error': 'Failed to load dashboard'}), 500

@app.route('/api/admin/properties', methods=['GET'])
@admin_required
def admin_properties_simple():
    """Simple admin properties endpoint that works with existing data"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 50)

        # Calculate skip value
        skip = (page - 1) * per_page

        # Simple query - get all properties for admin
        cursor = properties_collection.find({}).skip(skip).limit(per_page).sort('_id', -1)

        properties = []
        for prop in cursor:
            # Convert all ObjectId fields to strings
            def convert_objectids(obj):
                if isinstance(obj, ObjectId):
                    return str(obj)
                elif isinstance(obj, dict):
                    return {k: convert_objectids(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_objectids(item) for item in obj]
                else:
                    return obj

            # Convert the entire property document
            prop = convert_objectids(prop)

            # Simple field mapping for compatibility
            if 'rent' in prop and 'price' not in prop:
                prop['price'] = prop['rent']
            if 'location' in prop and 'city' not in prop:
                prop['city'] = prop['location']
            if 'ownerPhone' in prop and 'phone' not in prop:
                prop['phone'] = prop['ownerPhone']
            if 'imageUrls' in prop and 'image_urls' not in prop:
                prop['image_urls'] = prop['imageUrls']

            # Add default values for missing fields
            prop.setdefault('status', 'approved')  # Default to approved for old properties
            prop.setdefault('upvotes', 0)
            prop.setdefault('downvotes', 0)
            prop.setdefault('reports', [])

            properties.append(prop)

        # Get total count
        total = properties_collection.count_documents({})

        return jsonify({
            'success': True,
            'data': properties,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page,
            'has_next': page * per_page < total,
            'has_prev': page > 1
        })

    except Exception as e:
        logger.error(f"Error in admin_properties_simple: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/admin/properties/pending')
@admin_required
def admin_pending_properties():
    """Get pending properties for admin review"""
    try:
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 10)), 50)
        skip = (page - 1) * per_page

        # Get pending properties
        cursor = properties_collection.find({'status': 'pending'}).skip(skip).limit(per_page).sort('created_at', -1)

        properties = []
        for prop in cursor:
            prop['_id'] = str(prop['_id'])
            properties.append(prop)

        total = properties_collection.count_documents({'status': 'pending'})

        return jsonify({
            'success': True,
            'data': properties,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })

    except Exception as e:
        logger.error(f"Error fetching pending properties: {e}")
        return jsonify({'success': False, 'error': 'Failed to fetch pending properties'}), 500

@app.route('/admin/properties/<property_id>/approve', methods=['POST'])
@admin_required
def admin_approve_property(property_id):
    """Approve a pending property"""
    try:
        object_id = ObjectId(property_id)

        result = properties_collection.update_one(
            {'_id': object_id, 'status': 'pending'},
            {
                '$set': {
                    'status': 'approved',
                    'updated_at': datetime.now(timezone.utc)
                }
            }
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found or not pending'}), 404

        logger.info(f"Property approved: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Property approved successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error approving property: {e}")
        return jsonify({'success': False, 'error': 'Failed to approve property'}), 500

@app.route('/admin/properties/<property_id>/reject', methods=['POST'])
@admin_required
def admin_reject_property(property_id):
    """Reject a pending property"""
    try:
        object_id = ObjectId(property_id)

        result = properties_collection.update_one(
            {'_id': object_id, 'status': 'pending'},
            {
                '$set': {
                    'status': 'rejected',
                    'updated_at': datetime.now(timezone.utc)
                }
            }
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found or not pending'}), 404

        logger.info(f"Property rejected: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Property rejected successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error rejecting property: {e}")
        return jsonify({'success': False, 'error': 'Failed to reject property'}), 500

@app.route('/admin/properties/reported')
@admin_required
def admin_reported_properties():
    """Get reported properties for admin review"""
    try:
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 10)), 50)
        skip = (page - 1) * per_page

        # Get properties with reports
        cursor = properties_collection.find({'reports.0': {'$exists': True}}).skip(skip).limit(per_page).sort('created_at', -1)

        properties = []
        for prop in cursor:
            prop['_id'] = str(prop['_id'])
            prop['report_count'] = len(prop.get('reports', []))
            properties.append(prop)

        total = properties_collection.count_documents({'reports.0': {'$exists': True}})

        return jsonify({
            'success': True,
            'data': properties,
            'total': total,
            'page': page,
            'per_page': per_page,
            'pages': (total + per_page - 1) // per_page
        })

    except Exception as e:
        logger.error(f"Error fetching reported properties: {e}")
        return jsonify({'success': False, 'error': 'Failed to fetch reported properties'}), 500

@app.route('/admin/properties/<property_id>/dismiss-reports', methods=['POST'])
@admin_required
def admin_dismiss_reports(property_id):
    """Dismiss all reports for a property"""
    try:
        object_id = ObjectId(property_id)

        result = properties_collection.update_one(
            {'_id': object_id},
            {
                '$set': {
                    'reports': [],
                    'updated_at': datetime.now(timezone.utc)
                }
            }
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        logger.info(f"Reports dismissed for property: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Reports dismissed successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error dismissing reports: {e}")
        return jsonify({'success': False, 'error': 'Failed to dismiss reports'}), 500

@app.route('/admin/properties/<property_id>/delete', methods=['DELETE'])
@admin_required
def admin_delete_property(property_id):
    """Delete a property"""
    try:
        object_id = ObjectId(property_id)

        # Get property to delete images from Cloudinary
        property_doc = properties_collection.find_one({'_id': object_id})
        if not property_doc:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        # Delete images from Cloudinary
        if property_doc.get('image_urls'):
            for image_url in property_doc['image_urls']:
                try:
                    # Extract public_id from URL
                    public_id = image_url.split('/')[-1].split('.')[0]
                    cloudinary.uploader.destroy(f"properties/{property_id}/{public_id}")
                except Exception as e:
                    logger.warning(f"Failed to delete image from Cloudinary: {e}")

        # Delete property from database
        result = properties_collection.delete_one({'_id': object_id})

        if result.deleted_count == 0:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        logger.info(f"Property deleted: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Property deleted successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error deleting property: {e}")
        return jsonify({'success': False, 'error': 'Failed to delete property'}), 500

@app.route('/admin/properties/<property_id>/edit', methods=['PUT'])
@admin_required
def admin_edit_property(property_id):
    """Edit a property"""
    try:
        object_id = ObjectId(property_id)
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        # Build update document
        update_doc = {
            'updated_at': datetime.now(timezone.utc)
        }

        # Update allowed fields
        allowed_fields = ['title', 'price', 'city', 'area', 'bedrooms', 'bathrooms',
                         'furnished_status', 'description', 'status']

        for field in allowed_fields:
            if field in data:
                if field in ['price', 'bedrooms', 'bathrooms']:
                    update_doc[field] = float(data[field]) if field == 'price' else int(data[field])
                else:
                    update_doc[field] = data[field].strip() if isinstance(data[field], str) else data[field]

        # Update property
        result = properties_collection.update_one(
            {'_id': object_id},
            {'$set': update_doc}
        )

        if result.matched_count == 0:
            return jsonify({'success': False, 'error': 'Property not found'}), 404

        logger.info(f"Property edited: {property_id}")

        return jsonify({
            'success': True,
            'message': 'Property updated successfully'
        })

    except InvalidId:
        return jsonify({'success': False, 'error': 'Invalid property ID'}), 400
    except Exception as e:
        logger.error(f"Error editing property: {e}")
        return jsonify({'success': False, 'error': 'Failed to edit property'}), 500

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'Internal server error'}), 500

@app.errorhandler(413)
def too_large(error):
    return jsonify({'success': False, 'error': 'File too large'}), 413

# Debug endpoint for search testing
@app.route('/debug/search')
def debug_search():
    """Debug endpoint to test search functionality"""
    search_query = request.args.get('q', '')
    if not search_query:
        return jsonify({'error': 'Please provide a search query with ?q=your_search_term'})

    try:
        # Test the search parsing
        search_terms = parse_search_query(search_query)

        # Build base filter
        filter_query = {'status': 'approved'}
        filter_query.update(search_terms)

        # Get count and sample results
        total = properties_collection.count_documents(filter_query)
        sample_results = list(properties_collection.find(filter_query).limit(5))

        # Convert ObjectIds to strings
        for result in sample_results:
            result['_id'] = str(result['_id'])

        return jsonify({
            'search_query': search_query,
            'parsed_terms': search_terms,
            'final_filter': filter_query,
            'total_matches': total,
            'sample_results': sample_results
        })
    except Exception as e:
        return jsonify({'error': str(e)})

# Health check endpoint
@app.route('/health')
def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        db.command('ping')
        return jsonify({
            'success': True,
            'status': 'healthy',
            'database': 'connected',
            'timestamp': datetime.now(timezone.utc).isoformat()
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'success': False,
            'status': 'unhealthy',
            'database': 'disconnected',
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }), 503

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_ENV') == 'development'

    logger.info(f"Starting HavenHuts server on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)