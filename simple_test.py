#!/usr/bin/env python3
"""
Simple test to check if the server is working
"""

import socket
import time

def test_server_connection():
    """Test if server is listening on port 5000"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('127.0.0.1', 5000))
        sock.close()
        
        if result == 0:
            print("✅ Server is listening on port 5000")
            return True
        else:
            print("❌ Server is not listening on port 5000")
            return False
    except Exception as e:
        print(f"❌ Error testing connection: {e}")
        return False

def send_http_request():
    """Send a raw HTTP request to test the server"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect(('127.0.0.1', 5000))
        
        # Send HTTP GET request
        request = "GET / HTTP/1.1\r\nHost: 127.0.0.1:5000\r\nConnection: close\r\n\r\n"
        sock.send(request.encode())
        
        # Receive response
        response = b""
        while True:
            try:
                data = sock.recv(1024)
                if not data:
                    break
                response += data
            except socket.timeout:
                break
        
        sock.close()
        
        # Parse response
        response_str = response.decode('utf-8', errors='ignore')
        lines = response_str.split('\n')
        
        if lines:
            status_line = lines[0]
            print(f"✅ HTTP Response: {status_line.strip()}")
            
            # Check for redirects
            if '301' in status_line or '302' in status_line:
                for line in lines:
                    if line.lower().startswith('location:'):
                        print(f"   Redirect to: {line.strip()}")
            
            return True
        else:
            print("❌ No response received")
            return False
            
    except Exception as e:
        print(f"❌ Error sending HTTP request: {e}")
        return False

def main():
    print("Simple Server Test")
    print("=" * 30)
    
    # Test connection
    if test_server_connection():
        print()
        # Test HTTP request
        send_http_request()
    
    print("\nIf the server is working, you should be able to:")
    print("1. Open http://127.0.0.1:5000 in your browser")
    print("2. See the HavenHuts homepage")
    print("3. Access the admin panel at http://127.0.0.1:5000/admin")

if __name__ == "__main__":
    main()
