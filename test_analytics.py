#!/usr/bin/env python3
"""
Test script for the analytics system
"""

import requests
import time
import random
from datetime import datetime
import urllib3

# Disable SSL warnings for local testing
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configuration
BASE_URL = 'http://127.0.0.1:5000'
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (Android 11; Mobile; rv:89.0) Gecko/89.0 Firefox/89.0'
]

PAGES = [
    '/',
    '/post-property',
    '/search',
    '/property/test-property-1',
    '/property/test-property-2'
]

def simulate_visitor():
    """Simulate a visitor browsing the site"""
    session = requests.Session()

    # Disable SSL verification for local testing
    session.verify = False

    # Random user agent
    user_agent = random.choice(USER_AGENTS)
    session.headers.update({'User-Agent': user_agent})

    # Visit random pages
    num_pages = random.randint(1, 5)
    visited_pages = random.sample(PAGES, min(num_pages, len(PAGES)))

    print(f"Simulating visitor with {num_pages} page views...")

    for page in visited_pages:
        try:
            response = session.get(f"{BASE_URL}{page}")
            print(f"  Visited {page} - Status: {response.status_code}")

            # Random delay between page views
            time.sleep(random.uniform(1, 5))

        except requests.exceptions.RequestException as e:
            print(f"  Error visiting {page}: {e}")

    print(f"  Visitor session completed\n")

def test_analytics_api():
    """Test the analytics API endpoints"""
    print("Testing Analytics API...")

    # Test analytics summary
    try:
        response = requests.get(f"{BASE_URL}/api/analytics/summary",
                              auth=('admin', 'admin123'), verify=False)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Analytics Summary: {data}")
        else:
            print(f"✗ Analytics Summary failed: {response.status_code}")
    except Exception as e:
        print(f"✗ Analytics Summary error: {e}")

    # Test daily analytics
    try:
        response = requests.get(f"{BASE_URL}/api/analytics/daily?days=7",
                              auth=('admin', 'admin123'), verify=False)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Daily Analytics: {len(data.get('data', []))} days of data")
        else:
            print(f"✗ Daily Analytics failed: {response.status_code}")
    except Exception as e:
        print(f"✗ Daily Analytics error: {e}")

    # Test geographic analytics
    try:
        response = requests.get(f"{BASE_URL}/api/analytics/geographic",
                              auth=('admin', 'admin123'), verify=False)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Geographic Analytics: {data}")
        else:
            print(f"✗ Geographic Analytics failed: {response.status_code}")
    except Exception as e:
        print(f"✗ Geographic Analytics error: {e}")

def main():
    """Main test function"""
    print("HavenHuts Analytics System Test")
    print("=" * 40)
    print(f"Testing against: {BASE_URL}")
    print(f"Started at: {datetime.now()}")
    print()

    # Check if server is running
    try:
        response = requests.get(BASE_URL, timeout=5, verify=False)
        print(f"✓ Server is running (Status: {response.status_code})")
    except requests.exceptions.RequestException as e:
        print(f"✗ Server is not accessible: {e}")
        print("Please start the server with: python app.py")
        return

    print()

    # Simulate multiple visitors
    num_visitors = 10
    print(f"Simulating {num_visitors} visitors...")

    for i in range(num_visitors):
        print(f"Visitor {i+1}/{num_visitors}:")
        simulate_visitor()

        # Random delay between visitors
        time.sleep(random.uniform(0.5, 2))

    print("Waiting for analytics to process...")
    time.sleep(5)

    # Test analytics API
    test_analytics_api()

    print()
    print("Test completed!")
    print("You can now check the admin panel at: http://localhost:5000/admin")
    print("Username: admin, Password: admin123")
    print("Go to the Analytics tab to see the results.")

if __name__ == "__main__":
    main()
