#!/usr/bin/env python3
"""
Debug script to check database state and test API calls
"""

import requests
from pymongo import MongoClient
import os
from dotenv import load_dotenv

load_dotenv()

def check_database():
    """Check the database state"""
    try:
        client = MongoClient(os.getenv('MONGO_URI', 'mongodb://localhost:27017/'))
        db = client[os.getenv('MONGO_DB_NAME', 'eznest')]
        properties_collection = db.properties
        
        # Count total properties
        total = properties_collection.count_documents({})
        print(f"Total properties in database: {total}")
        
        # Count by status
        pending = properties_collection.count_documents({'status': 'pending'})
        approved = properties_collection.count_documents({'status': 'approved'})
        rejected = properties_collection.count_documents({'status': 'rejected'})
        
        print(f"Pending: {pending}, Approved: {approved}, Rejected: {rejected}")
        
        # Get a sample property
        sample = properties_collection.find_one({})
        if sample:
            print(f"Sample property keys: {list(sample.keys())}")
        else:
            print("No properties found in database")
            
        return total > 0
        
    except Exception as e:
        print(f"Database error: {e}")
        return False

def test_api_calls():
    """Test API calls with different parameters"""
    BASE_URL = "http://localhost:5000"
    
    # Login first
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'Interpreter@1435'}
    
    print("\nTesting API calls...")
    login_response = session.post(f"{BASE_URL}/admin/login", json=login_data)
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.status_code}")
        return
    
    print("Login successful!")
    
    # Test different page sizes
    test_cases = [
        {'per_page': 5, 'name': 'Small page (5)'},
        {'per_page': 10, 'name': 'Medium page (10)'},
        {'per_page': 25, 'name': 'Large page (25)'},
    ]
    
    for test_case in test_cases:
        print(f"\nTesting: {test_case['name']}")
        params = {
            'admin': 'true',
            'page': 1,
            'per_page': test_case['per_page']
        }
        
        try:
            response = session.get(f"{BASE_URL}/api/properties", params=params)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ Success: {len(data.get('data', []))} properties")
                    print(f"Total: {data.get('total', 0)}, Pages: {data.get('pages', 0)}")
                else:
                    print(f"❌ API Error: {data.get('error')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

if __name__ == "__main__":
    print("🔍 Database and API Debug Tool")
    print("=" * 40)
    
    has_data = check_database()
    if has_data:
        test_api_calls()
    else:
        print("No data in database to test with")
