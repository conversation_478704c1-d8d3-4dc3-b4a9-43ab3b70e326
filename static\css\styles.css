/* HavenHuts Basic Styles - Fallback for when Tail<PERSON> doesn't load */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f9fafb;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

/* Navigation */
.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    font-size: 1.5rem;
    font-weight: bold;
    color: #4f46e5;
    text-decoration: none;
}

.nav-links {
    display: flex;
    gap: 1rem;
    list-style: none;
}

.nav-links a {
    color: #6b7280;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.2s;
}

.nav-links a:hover {
    background-color: #f3f4f6;
    color: #4f46e5;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    text-decoration: none;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background-color: #4338ca;
}

/* Property cards */
.property-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.property-card {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.property-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.property-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.property-content {
    padding: 1rem;
}

.property-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.property-price {
    font-size: 1.25rem;
    font-weight: bold;
    color: #059669;
    margin-bottom: 0.5rem;
}

.property-location {
    color: #6b7280;
    margin-bottom: 0.5rem;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.form-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 1rem;
}

.form-input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Utilities */
.text-center { text-align: center; }
.text-primary { color: #4f46e5; }
.text-gray { color: #6b7280; }
.mb-4 { margin-bottom: 1rem; }
.mt-4 { margin-top: 1rem; }
.p-4 { padding: 1rem; }

/* Font Awesome Fallback Icons */
.fas, .fab, .far {
    font-family: Arial, sans-serif;
    font-style: normal;
    font-weight: normal;
    display: inline-block;
    text-decoration: inherit;
}

/* Enhanced fallback when Font Awesome completely fails */
body.fa-fallback .fas::before,
body.fa-fallback .fab::before,
body.fa-fallback .far::before {
    font-family: Arial, sans-serif !important;
    font-weight: normal !important;
}

/* Common icon fallbacks */
.fa-home::before { content: "🏠"; }
.fa-search::before { content: "🔍"; }
.fa-bars::before { content: "☰"; }
.fa-times::before { content: "✕"; }
.fa-plus::before { content: "+"; }
.fa-info-circle::before { content: "ℹ"; }
.fa-envelope::before { content: "✉"; }
.fa-map-marker-alt::before { content: "📍"; }
.fa-thumbs-up::before { content: "👍"; }
.fa-thumbs-down::before { content: "👎"; }
.fa-flag::before { content: "🚩"; }
.fa-whatsapp::before { content: "💬"; }
.fa-chevron-left::before { content: "‹"; }
.fa-chevron-right::before { content: "›"; }
.fa-shield-alt::before { content: "🛡"; }
.fa-sign-out-alt::before { content: "↪"; }
.fa-lock::before { content: "🔒"; }
.fa-user::before { content: "👤"; }
.fa-exclamation-circle::before { content: "⚠"; }
.fa-facebook::before { content: "f"; }
.fa-twitter::before { content: "t"; }
.fa-instagram::before { content: "i"; }

/* Responsive */
@media (max-width: 768px) {
    .nav {
        flex-direction: column;
        gap: 1rem;
    }

    .property-grid {
        grid-template-columns: 1fr;
    }
}
