name: Deploy HavenHuts to CapRover (Alternative)

on:
  workflow_dispatch:  # Manual deployment only
  
jobs:
  deploy:
    runs-on: ubuntu-latest
    
    env:
      NODE_TLS_REJECT_UNAUTHORIZED: '0'

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install Dependencies
        run: |
          pip install --upgrade pip
          pip install -r requirements.txt

      - name: Run Basic Tests
        run: |
          python -c "import app; print('✅ App imports successfully')"
          echo "✅ Basic validation passed"

      # Simple deployment bundle creation using built-in tar
      - name: Create deployment bundle (Simple)
        run: |
          echo "📦 Creating deployment bundle..."
          
          # List all files to include
          echo "📋 Files to include:"
          ls -la
          
          # Create deployment bundle with only existing files
          tar -czf deploy.tar \
            app.py \
            wsgi.py \
            requirements.txt \
            Dockerfile \
            captain-definition \
            $([ -f .env.example ] && echo .env.example) \
            $([ -f sitemap.xml ] && echo sitemap.xml) \
            templates/ \
            static/ \
            || echo "⚠️ Some files may be missing, continuing..."
          
          echo "✅ Deployment bundle created!"
          echo "📋 Bundle contents:"
          tar -tzf deploy.tar | head -20

      # Deploy to CapRover
      - name: Deploy to CapRover
        uses: caprover/deploy-from-github@v1.1.2
        with:
          server: ${{ secrets.CAPROVER_SERVER }}
          app: ${{ secrets.APP_NAME }}
          token: ${{ secrets.APP_TOKEN }}

      - name: Deployment Success
        if: success()
        run: |
          echo "🎉 HavenHuts deployed successfully!"
          echo "🌐 App URL: https://${{ secrets.APP_NAME }}.${{ secrets.CAPROVER_SERVER }}"
          echo "🔍 Health check: https://${{ secrets.APP_NAME }}.${{ secrets.CAPROVER_SERVER }}/api/health"

      - name: Deployment Failure
        if: failure()
        run: |
          echo "❌ Deployment failed!"
          echo "🔧 Check the logs above for details"
