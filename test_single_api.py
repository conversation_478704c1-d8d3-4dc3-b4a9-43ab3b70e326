#!/usr/bin/env python3
"""
Simple test to check specific API calls that are failing
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_specific_calls():
    """Test specific API calls that are causing 500 errors"""
    
    # Login first
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'Interpreter@1435'}
    
    print("Logging in...")
    login_response = session.post(f"{BASE_URL}/admin/login", json=login_data)
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.status_code}")
        return
    
    print("Login successful!")
    
    # Test cases that were failing
    test_cases = [
        {
            'name': 'Large page size (25)',
            'params': {'admin': 'true', 'page': 1, 'per_page': 25}
        },
        {
            'name': 'Search functionality',
            'params': {'admin': 'true', 'page': 1, 'per_page': 10, 'search': 'kolkata'}
        },
        {
            'name': 'Large page size (50)',
            'params': {'admin': 'true', 'page': 1, 'per_page': 50}
        }
    ]
    
    for test_case in test_cases:
        print(f"\nTesting: {test_case['name']}")
        print(f"Params: {test_case['params']}")
        
        try:
            response = session.get(f"{BASE_URL}/api/properties", params=test_case['params'])
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ Success: {len(data.get('data', []))} properties")
                else:
                    print(f"❌ API Error: {data.get('error')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text[:500]}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_specific_calls()
