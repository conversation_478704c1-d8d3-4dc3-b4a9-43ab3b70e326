#!/usr/bin/env python3
"""
Minimal test to isolate the issue
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_minimal():
    """Test with minimal parameters"""
    
    # Login first
    session = requests.Session()
    login_data = {'username': 'admin', 'password': 'Interpreter@1435'}
    
    print("Testing minimal API calls...")
    login_response = session.post(f"{BASE_URL}/admin/login", json=login_data)
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.status_code}")
        return
    
    print("Login successful!")
    
    # Test with minimal parameters - just admin=true and per_page=25
    print("\nTesting with minimal parameters:")
    params = {
        'admin': 'true',
        'per_page': 25
    }
    
    try:
        response = session.get(f"{BASE_URL}/api/properties", params=params)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Success: {len(data.get('data', []))} properties")
                print(f"Total: {data.get('total', 0)}, Pages: {data.get('pages', 0)}")
            else:
                print(f"❌ API Error: {data.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

    # Test with even more minimal parameters
    print("\nTesting with absolute minimal parameters:")
    params = {
        'admin': 'true'
    }
    
    try:
        response = session.get(f"{BASE_URL}/api/properties", params=params)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f"✅ Success: {len(data.get('data', []))} properties")
                print(f"Total: {data.get('total', 0)}, Pages: {data.get('pages', 0)}")
            else:
                print(f"❌ API Error: {data.get('error')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_minimal()
