#!/usr/bin/env python3
"""
Test the improved clipboard functionality
"""

import requests

BASE_URL = "http://localhost:5000"

def test_clipboard_improvements():
    """Test clipboard functionality improvements"""
    
    print("📋 Clipboard Functionality Test")
    print("=" * 40)
    
    # Create a test property to get an edit hash
    print("\n1. Creating test property...")
    
    property_data = {
        'title': 'Test Property for Clipboard Testing',
        'price': '15000',
        'city': 'Kolkata',
        'phone': '9876543210',
        'description': 'Test property for clipboard functionality testing.'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/properties", data=property_data)
        
        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                property_id = result.get('property_id')
                edit_hash = result.get('edit_hash')
                
                print(f"✅ Property created successfully!")
                print(f"   Property ID: {property_id}")
                print(f"   Edit Hash: {edit_hash}")
                
                print(f"\n📋 Clipboard Testing Instructions:")
                print("=" * 50)
                print("1. Open your browser to: http://localhost:5000/post-property")
                print("2. Fill out a simple form and submit it")
                print("3. In the success modal, test the clipboard functionality:")
                print("   ✓ Try clicking the 'Copy Property Code' button")
                print("   ✓ If it fails, you should see a nice modal for manual copy")
                print("   ✓ Try clicking the property code directly to select it")
                print("   ✓ Use Ctrl+C to copy manually")
                print("4. Test the edit functionality:")
                print(f"   ✓ Paste this edit hash: {edit_hash}")
                print("   ✓ Verify the property loads correctly")
                
                print(f"\n🔧 Improvements Made:")
                print("   ✅ Multiple clipboard methods (modern API + fallback)")
                print("   ✅ User-friendly manual copy modal")
                print("   ✅ Click-to-select functionality")
                print("   ✅ Clear instructions and visual feedback")
                print("   ✅ Works on HTTP and HTTPS")
                print("   ✅ Compatible with older browsers")
                
                print(f"\n🌐 Test URL: http://localhost:5000/post-property")
                
                # Clean up the test property
                delete_response = requests.delete(f"{BASE_URL}/api/properties/{property_id}?hash={edit_hash}")
                if delete_response.status_code == 200:
                    print(f"\n🗑️  Test property cleaned up")
                
                return True
            else:
                print(f"❌ Failed to create property: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    test_clipboard_improvements()
