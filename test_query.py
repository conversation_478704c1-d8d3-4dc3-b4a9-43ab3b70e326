#!/usr/bin/env python3
"""
Test the MongoDB query directly to find the issue
"""

from pymongo import MongoClient
import os
from dotenv import load_dotenv
from datetime import datetime

load_dotenv()

def test_mongodb_query():
    """Test the MongoDB query that's causing issues"""
    try:
        client = MongoClient(os.getenv('MONGO_URI', 'mongodb://localhost:27017/'))
        db = client[os.getenv('MONGO_DB_NAME', 'eznest')]
        properties_collection = db.properties
        
        print("Testing MongoDB queries...")
        
        # Test 1: Simple query with small limit
        print("\n1. Simple query with limit 5:")
        try:
            cursor = properties_collection.find({}).limit(5)
            properties = list(cursor)
            print(f"✅ Success: {len(properties)} properties")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 2: Query with larger limit
        print("\n2. Query with limit 25:")
        try:
            cursor = properties_collection.find({}).limit(25)
            properties = list(cursor)
            print(f"✅ Success: {len(properties)} properties")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 3: Query with sorting
        print("\n3. Query with sorting and limit 25:")
        try:
            cursor = properties_collection.find({}).sort('created_at', -1).limit(25)
            properties = list(cursor)
            print(f"✅ Success: {len(properties)} properties")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 4: Check for problematic fields
        print("\n4. Checking for problematic fields:")
        try:
            sample = properties_collection.find_one({})
            if sample:
                print(f"Sample property fields: {list(sample.keys())}")
                
                # Check for datetime fields
                for key, value in sample.items():
                    if isinstance(value, datetime):
                        print(f"Datetime field: {key} = {value}")
                    elif key == 'created_at' and value is None:
                        print(f"NULL created_at field found!")
                        
        except Exception as e:
            print(f"❌ Error checking fields: {e}")
        
        # Test 5: Query with field mapping
        print("\n5. Testing field mapping logic:")
        try:
            cursor = properties_collection.find({}).limit(25)
            count = 0
            for prop in cursor:
                count += 1
                # Apply the same field mapping as in the API
                if 'rent' in prop and 'price' not in prop:
                    prop['price'] = prop['rent']
                if 'location' in prop and 'city' not in prop:
                    prop['city'] = prop['location']
                if 'ownerPhone' in prop and 'phone' not in prop:
                    prop['phone'] = prop['ownerPhone']
                if 'imageUrls' in prop and 'image_urls' not in prop:
                    prop['image_urls'] = prop['imageUrls']
                
                # Ensure required fields have default values
                prop.setdefault('price', 0)
                prop.setdefault('city', 'Not specified')
                prop.setdefault('phone', 'Not provided')
                prop.setdefault('status', 'pending')
                prop.setdefault('upvotes', 0)
                prop.setdefault('downvotes', 0)
                prop.setdefault('reports', [])
                prop.setdefault('created_at', datetime.now())
                
            print(f"✅ Field mapping successful for {count} properties")
        except Exception as e:
            print(f"❌ Field mapping error: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")
        
    except Exception as e:
        print(f"Database connection error: {e}")

if __name__ == "__main__":
    test_mongodb_query()
