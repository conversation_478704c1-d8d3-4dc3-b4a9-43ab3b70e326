#!/usr/bin/env python3
"""
Test frontend integration for property management
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_frontend_integration():
    """Test that the frontend page loads correctly"""

    print("🌐 Frontend Integration Test")
    print("=" * 40)

    # Test 1: Check if post property page loads
    print("\n1. Testing Post Property Page...")

    try:
        response = requests.get(f"{BASE_URL}/post-property")

        if response.status_code == 200:
            content = response.text

            # Check for key elements
            checks = [
                ('Property Code Input', 'propertyCode' in content),
                ('Status Messages', 'codeStatus' in content),
                ('Delete Button', 'deleteBtn' in content),
                ('Form Elements', 'propertyForm' in content),
                ('JavaScript Functions', 'handlePropertyCodeInput' in content),
                ('Edit Mode Variables', 'editMode' in content),
                ('Management Functions', 'loadPropertyForEdit' in content)
            ]

            print("✅ Post property page loaded successfully!")

            for check_name, check_result in checks:
                status = "✅" if check_result else "❌"
                print(f"   {status} {check_name}: {'Found' if check_result else 'Missing'}")

            # Check if all required elements are present
            all_present = all(check[1] for check in checks)

            if all_present:
                print("\n✅ All required frontend elements are present!")
            else:
                print("\n⚠️  Some frontend elements may be missing")

        else:
            print(f"❌ Failed to load post property page: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Exception loading page: {e}")
        return False

    # Test 2: Create a property to get a real edit hash for frontend testing
    print("\n2. Creating Test Property for Frontend Testing...")

    property_data = {
        'title': 'Frontend Test Property',
        'price': '12000',
        'city': 'Kolkata',
        'phone': '+91 98765 43210',
        'description': 'Test property for frontend integration testing.'
    }

    try:
        response = requests.post(f"{BASE_URL}/api/properties", data=property_data)

        if response.status_code == 201:
            result = response.json()
            if result.get('success'):
                edit_hash = result.get('edit_hash')
                property_id = result.get('property_id')

                print(f"✅ Test property created!")
                print(f"   Property ID: {property_id}")
                print(f"   Edit Hash: {edit_hash}")

                print("\n📋 Frontend Testing Instructions:")
                print("=" * 50)
                print("1. Open your browser to: http://localhost:5000/post-property")
                print("2. Paste this edit hash in the property code field:")
                print(f"   {edit_hash}")
                print("3. Verify that:")
                print("   ✓ Property loads automatically")
                print("   ✓ Form fields are pre-filled")
                print("   ✓ Submit button changes to 'Update Property'")
                print("   ✓ Delete button appears")
                print("   ✓ Success message shows 'Property loaded successfully'")
                print("4. Try updating the property")
                print("5. Try deleting the property")
                print("\n🔗 Direct link: http://localhost:5000/post-property")

                return True
            else:
                print(f"❌ Failed to create test property: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP error creating test property: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Exception creating test property: {e}")
        return False

if __name__ == "__main__":
    test_frontend_integration()
